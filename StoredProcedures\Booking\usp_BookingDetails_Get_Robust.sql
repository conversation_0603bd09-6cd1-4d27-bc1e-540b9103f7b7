-- =============================================
-- Robust Stored Procedure: usp_BookingDetails_Get
-- Description: Handles both new and old bookings with proper user identification
-- =============================================

USE [CabYaari]
GO

ALTER PROCEDURE [dbo].[usp_BookingDetails_Get]
    @bookingID nvarchar(30)
WITH RECOMPILE
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    SELECT 
        b.PK<PERSON>,
        b.Booking_Id AS BookingID,
        cf.CitY_Name AS PickUpCity,
        ct.CitY_Name AS DropOffCity,
        tt.Trip_Type AS TripType,
        cc.Car_Category_Abbr AS CarCategory,
        b.Duration,
        b.Distance,
        b.Basic_Fare AS BasicFare,
        b.Driver_Charge AS DriverCharge,
        b.GST AS Gst,
        b.<PERSON>,
        b.GST_Fare AS GstFare,
        b.Coupon_Code AS CouponCode,
        b.Coupon_Discount AS CouponDiscount,
        b.Booking_Date AS BookingDate,
        b.PickUp_Address AS PickUpAddress,
        b.DropOff_Address AS DropOffAddress,
        b.PickUp_Date AS PickUpDate,
        b.PickUp_Time AS PickUpTime,
        b.[Name] AS TravelerName,
        b.Mobile_No1 AS PhoneNumber,
        b.Mail_Id AS MailId,
        b.Mode_Of_Payment_Id AS PaymentMode,
        b.Booking_Status_Id AS BookingStatusId,
        -- ✅ ROBUST: Try multiple approaches to get the correct user identifier
        CASE 
            -- Priority 1: Use Booking_Created_By if available (new bookings)
            WHEN b.Booking_Created_By IS NOT NULL AND b.Booking_Created_By != '' 
                THEN b.Booking_Created_By
            -- Priority 2: Try to find user by email (old bookings)
            WHEN b.Mail_Id IS NOT NULL AND b.Mail_Id != ''
                THEN (SELECT TOP 1 Id FROM [Identity].[User] WHERE Email = b.Mail_Id)
            -- Priority 3: Fallback to Created_By as string (legacy)
            ELSE CAST(b.Created_By AS NVARCHAR)
        END AS BookingCreatedBy,
        b.razorpay_payment_id AS RazorpayPaymentId,
        b.razorpay_order_id AS RazorpayOrderid,
        b.razorpay_signature AS RazorpaySignature,
        b.razorpay_status AS RazorpayStatus,
        b.PickUpAddressLatitude AS PickUpAddressLongLat,
        b.PickUpAddressLongitude AS DropOffAddressLongLat,
        b.CashAmountToPayDriver,
        b.PaymentOption,
        b.TollCharge,
        b.PaymentType,
        b.PartialPaymentAmount,
        b.RemainingAmountForDriver
    FROM 
        [dbo].[RLT_BOOKING] b
    INNER JOIN 
        [dbo].[RLT_CITY] cf ON b.City_From_Id = cf.PKID
    INNER JOIN 
        [dbo].[RLT_CITY] ct ON b.City_To_Id = ct.PKID
    INNER JOIN 
        [dbo].[RLT_TRIP_TYPES] tt ON b.Trip_Type_Id = tt.PKID
    INNER JOIN 
        [dbo].[RLT_CAR_CATEGORY] cc ON b.Car_Category_Id = cc.PKID
    WHERE 
        b.Booking_Id = @bookingID
END
GO

PRINT 'usp_BookingDetails_Get updated with robust user identification!'

-- =============================================
-- How this works:
-- 
-- For NEW bookings (with Booking_Created_By):
-- - Returns the actual GUID: '737a3632-cc03-41f3-b8f1-1364ab6b36a2'
-- 
-- For OLD bookings (without Booking_Created_By):
-- - Tries to find user by email match
-- - Returns the user's GUID if found
-- - Falls back to Created_By as string if no match
-- 
-- This allows the ownership validation to work for both old and new bookings
-- =============================================
