﻿using System;

namespace Domain.Entities
{
    public class RLT_BOOKING
    {

        public string BookingID { get; set; }
        public string PickUpCity { get; set; }
        public string DropOffCity { get; set; }
        public string TripType { get; set; }
        public string CarCategory { get; set; }
        public string Duration { get; set; }
        public decimal? Distance { get; set; }
        public decimal? BasicFare { get; set; }
        public decimal? DriverCharge { get; set; }
        public decimal? Gst { get; set; }
        public decimal? Fare { get; set; }
        public decimal? GstFare { get; set; }
        public string CouponCode { get; set; }
        public decimal? CouponDiscount { get; set; }
        public string PickUpAddress { get; set; }
        public string DropOffAddress { get; set; }
        public DateTime? PickUpDate { get; set; }
        public string PickUpTime { get; set; }
        public string TravelerName { get; set; }
        public string PhoneNumber { get; set; }
        public string MailId { get; set; }
        public int? PaymentMode { get; set; }
        public string BookingCreatedBy { get; set; }
        public string RazorpayPaymentId { get; set; }
        public string RazorpayOrderid { get; set; }
        public string RazorpaySignature { get; set; }
        public string RazorpayStatus { get; set; }
        public string PickUpAddressLongLat { get; set; }
        public string DropOffAddressLongLat { get; set; }
        public decimal CashAmountToPayDriver { get; set; }
        public int PaymentOption  { get; set; }
        public decimal? TollCharge { get; set; }
        public string PaymentType { get; set; } // "PARTIAL" or "FULL"
        public decimal? PartialPaymentAmount { get; set; }
        public decimal? RemainingAmountForDriver { get; set; }
    }
}
