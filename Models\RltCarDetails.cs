﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltCarDetails
    {
        public int Pkid { get; set; }
        public Guid? PkGuid { get; set; }
        public int? VendorPkid { get; set; }
        public int? CarOwnerPkid { get; set; }
        public string CarNumber { get; set; }
        public string <PERSON><PERSON><PERSON><PERSON>Year { get; set; }
        public string CarManufacturing<PERSON>ear { get; set; }
        public int? CarCompanyPkid { get; set; }
        public int? CarModelPkid { get; set; }
        public string CarRegisteredDocument { get; set; }
        public int? CarRegisteredCityPkid { get; set; }
        public string CarFuelTypeStatus { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdateDate { get; set; }
        public int? CreatedBy { get; set; }
        public int? UpdatedBy { get; set; }
    }
}
