﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class Rlt_Bookings
    {
        public long PKID { get; set; }
        public string Booking_Id { get; set; }
        public int? City_From_Id { get; set; }
        public int? City_To_Id { get; set; }
        public int? Trip_Type_Id { get; set; }
        public int? Car_Category_Id { get; set; }
        public string Duration { get; set; }
        public decimal? Distance { get; set; }
        public decimal? Basic_Fare { get; set; }
        public decimal? Driver_Charge { get; set; }
        public decimal? Toll_Charge { get; set; }
        public decimal? Gst { get; set; }
        public decimal? Fare { get; set; }
        public decimal? Gst_Fare { get; set; }
        public string Coupon_Code { get; set; }
        public decimal? Coupon_Discount { get; set; }
        public DateTime? Booking_Date { get; set; }
        public string PickUp_Address { get; set; }
        public string DropOff_Address { get; set; }
        public DateTime? PickUp_Date { get; set; }
        public string PickUp_Time { get; set; }
        public string Name { get; set; }
        public string Mobile_No1 { get; set; }
        public string Mobile_No2 { get; set; }
        public string Mail_Id { get; set; }
        public int? Mode_Of_Payment_Id { get; set; }
        public int? Vendor_Id { get; set; }
        public int? Car_Id { get; set; }
        public int? Booking_Status_Id { get; set; }
        public string Booking_Remark { get; set; }
        public string Invoice_No { get; set; }
        public DateTime? Invoice_Date { get; set; }
        public bool? Is_Active { get; set; }
        public DateTime? Created_Date { get; set; }
        public DateTime? Updated_Date { get; set; }
        public int? Created_By { get; set; }
        public int? Updated_By { get; set; }
        public string razorpay_payment_id { get; set; }
        public string razorpay_order_id { get; set; }
        public string razorpay_signature { get; set; }
        public string razorpay_status { get; set; }
        public string PickUpAddressLatitude { get; set; }
        public string PickUpAddressLongitude { get; set; }
        public string BookingEditRemark { get; set; }
        public int? Driver_Id { get; set; }
        public string Completepickupaddress { get; set; }
        public string Completedropoffpaddress { get; set; }
    }
}
