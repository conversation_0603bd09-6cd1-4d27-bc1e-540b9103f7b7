﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Application.Features.Bookings.Model
{
  public  class NewBooking
    {
        public string BookingID { get; set; }
        public string PickUpCity { get; set; }
        public string DropOffCity { get; set; }
        public string TripType { get; set; }
        public string CarCategory { get; set; }
        public string Duration { get; set; }
        public decimal? Distance { get; set; }
        public decimal? BasicFare { get; set; }
        public decimal? DriverCharge { get; set; }
        public decimal? Gst { get; set; }
        public decimal? Fare { get; set; }
        public decimal? GstFare { get; set; }
        public string CouponCode { get; set; }
        public decimal? CouponDiscount { get; set; }
        public string PickUpAddress { get; set; }
        public string DropOffAddress { get; set; }
        public DateTime? PickUpDate { get; set; }
        public string PickUpTime { get; set; }
        public string TravelerName { get; set; }
        public string PhoneNumber { get; set; }
        public string MailId { get; set; }
        public int? PaymentMode { get; set; }
        public string BookingCreatedBy { get; set; }
        public string RazorpayOrderid { get; set; }
        public string RazorpayStatus { get; set; }
        public string PickUpAddressLongLat { get; set; }
        public string DropOffAddressLongLat { get; set; }
        public decimal CashAmountToPayDriver { get; set; }
        public int PaymentOption { get; set; }
        public decimal? TollCharge { get; set; }
    }
}
