﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace Infrastructure.Persistence.Models
{
    public partial class rltcarrentalContext : DbContext
    {
        public rltcarrentalContext()
        {
        }

        public rltcarrentalContext(DbContextOptions<rltcarrentalContext> options)
            : base(options)
        {
        }

        public virtual DbSet<Products> Products { get; set; }
        public virtual DbSet<RltAboutUs> RltAboutUs { get; set; }
        public virtual DbSet<RltAdminRole> RltAdminRole { get; set; }
        public virtual DbSet<RltAdminUser> RltAdminUser { get; set; }
        public virtual DbSet<RltBankNames> RltBankNames { get; set; }
        public virtual DbSet<RltBooking> RltBooking { get; set; }
        public virtual DbSet<RltBookingFare> RltBookingFare { get; set; }
        public virtual DbSet<RltBookingGst> RltBookingGst { get; set; }
        public virtual DbSet<RltBookingHistrory> RltBookingHistrory { get; set; }
        public virtual DbSet<RltBookingMode> RltBookingMode { get; set; }
        public virtual DbSet<RltBookingPaymentDetails> RltBookingPaymentDetails { get; set; }
        public virtual DbSet<RltBookingRules> RltBookingRules { get; set; }
        public virtual DbSet<RltBookingStatus> RltBookingStatus { get; set; }
        public virtual DbSet<Rlt_Bookings> RltBookings { get; set; }
        public virtual DbSet<RltCarCategory> RltCarCategory { get; set; }
        public virtual DbSet<RltCarChargesFecilitiesDetails> RltCarChargesFecilitiesDetails { get; set; }
        public virtual DbSet<RltCarCompany> RltCarCompany { get; set; }
        public virtual DbSet<RltCarDetails> RltCarDetails { get; set; }
        public virtual DbSet<RltCarDocs> RltCarDocs { get; set; }
        public virtual DbSet<RltCarDriverDetails> RltCarDriverDetails { get; set; }
        public virtual DbSet<RltCarDriverDocs> RltCarDriverDocs { get; set; }
        public virtual DbSet<RltCarFuelTypes> RltCarFuelTypes { get; set; }
        public virtual DbSet<RltCarModel> RltCarModel { get; set; }
        public virtual DbSet<RltCarOwnerBankDetails> RltCarOwnerBankDetails { get; set; }
        public virtual DbSet<RltCarOwnerDetails> RltCarOwnerDetails { get; set; }
        public virtual DbSet<RltCarSegment> RltCarSegment { get; set; }
        public virtual DbSet<RltCity> RltCity { get; set; }
        public virtual DbSet<RltClassMaster> RltClassMaster { get; set; }
        public virtual DbSet<RltCompanyDetails> RltCompanyDetails { get; set; }
        public virtual DbSet<RltContactEnquiries> RltContactEnquiries { get; set; }
        public virtual DbSet<RltContactInformation> RltContactInformation { get; set; }
        public virtual DbSet<RltCountry> RltCountry { get; set; }
        public virtual DbSet<RltDepartmentName> RltDepartmentName { get; set; }
        public virtual DbSet<RltDiscountCoupon> RltDiscountCoupon { get; set; }
        public virtual DbSet<RltDocumnetsName> RltDocumnetsName { get; set; }
        public virtual DbSet<RltDriverApproveStatus> RltDriverApproveStatus { get; set; }
        public virtual DbSet<RltDriverEnquiries> RltDriverEnquiries { get; set; }
        public virtual DbSet<RltEmployee> RltEmployee { get; set; }
        public virtual DbSet<RltEntityGenerals> RltEntityGenerals { get; set; }
        public virtual DbSet<RltFaqDetails> RltFaqDetails { get; set; }
        public virtual DbSet<RltLocationCode> RltLocationCode { get; set; }
        public virtual DbSet<RltLog> RltLog { get; set; }
        public virtual DbSet<RltMenuMaster> RltMenuMaster { get; set; }
        public virtual DbSet<RltNextId> RltNextId { get; set; }
        public virtual DbSet<RltPaymentMethod> RltPaymentMethod { get; set; }
        public virtual DbSet<RltPrivacyPolicy> RltPrivacyPolicy { get; set; }
        public virtual DbSet<RltRoleMenuMapping> RltRoleMenuMapping { get; set; }
        public virtual DbSet<RltRoutePlan> RltRoutePlan { get; set; }
        public virtual DbSet<RltRoutesDetails> RltRoutesDetails { get; set; }
        public virtual DbSet<RltServices> RltServices { get; set; }
        public virtual DbSet<RltState> RltState { get; set; }
        public virtual DbSet<RltSubscribers> RltSubscribers { get; set; }
        public virtual DbSet<RltTemplateMailMessage> RltTemplateMailMessage { get; set; }
        public virtual DbSet<RltTemplateMaster> RltTemplateMaster { get; set; }
        public virtual DbSet<RltTermsConditions> RltTermsConditions { get; set; }
        public virtual DbSet<RltTripTypes> RltTripTypes { get; set; }
        public virtual DbSet<RltUserFeedBack> RltUserFeedBack { get; set; }
        public virtual DbSet<RltUserLoginDetails> RltUserLoginDetails { get; set; }
        public virtual DbSet<RltVendorBankDetails> RltVendorBankDetails { get; set; }
        public virtual DbSet<RltVendorCarDetails> RltVendorCarDetails { get; set; }
        public virtual DbSet<RltVendorDetails> RltVendorDetails { get; set; }
        public virtual DbSet<RltVendorDocs> RltVendorDocs { get; set; }
        public virtual DbSet<Role> Role { get; set; }
        public virtual DbSet<RoleClaims> RoleClaims { get; set; }
        public virtual DbSet<TempBooking> TempBooking { get; set; }
        public virtual DbSet<User> User { get; set; }
        public virtual DbSet<UserClaims> UserClaims { get; set; }
        public virtual DbSet<UserLogins> UserLogins { get; set; }
        public virtual DbSet<UserRoles> UserRoles { get; set; }
        public virtual DbSet<UserTokens> UserTokens { get; set; }
        public virtual DbSet<VendorCarDriverDocs> VendorCarDriverDocs { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. See http://go.microsoft.com/fwlink/?LinkId=723263 for guidance on storing connection strings.
                optionsBuilder.UseSqlServer("Server=202.66.175.57,1232;Initial Catalog=rltcarrental;user id=sagar;password=********");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Products>(entity =>
            {
                entity.Property(e => e.Rate).HasColumnType("decimal(18, 6)");
            });

            modelBuilder.Entity<RltAboutUs>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_AboutUs");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.AboutUsDetails).HasColumnType("text");

                entity.Property(e => e.IsActive).HasDefaultValueSql("((0))");
            });

            modelBuilder.Entity<RltAdminRole>(entity =>
            {
                entity.HasKey(e => e.RoleId);

                entity.ToTable("RLT_ADMIN_ROLE");

                entity.Property(e => e.CreatedBy).HasMaxLength(50);

                entity.Property(e => e.CreatedDate).HasColumnType("date");

                entity.Property(e => e.Role).HasMaxLength(50);

                entity.Property(e => e.RoleDescription)
                    .HasColumnName("Role_Description")
                    .HasMaxLength(500);
            });

            modelBuilder.Entity<RltAdminUser>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_ADMIN_USER");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.AadharId).HasMaxLength(50);

                entity.Property(e => e.Address).HasMaxLength(500);

                entity.Property(e => e.CreatedDate).HasColumnType("datetime");

                entity.Property(e => e.Is2TfaAuthentication).HasDefaultValueSql("((0))");

                entity.Property(e => e.IsAdmin).HasDefaultValueSql("((0))");

                entity.Property(e => e.UserEmailId)
                    .HasColumnName("UserEmailID")
                    .HasMaxLength(100);

                entity.Property(e => e.UserFirstName).HasMaxLength(50);

                entity.Property(e => e.UserLastName).HasMaxLength(50);

                entity.Property(e => e.UserMobileNo).HasMaxLength(20);

                entity.Property(e => e.UserName).HasMaxLength(50);

                entity.Property(e => e.UserPhoto).HasMaxLength(500);

                entity.Property(e => e.UserPwd)
                    .HasColumnName("UserPWD")
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<RltBankNames>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_BANK_NAMES");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.BankName)
                    .HasColumnName("Bank_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.PkGuid)
                    .HasColumnName("PK_GUID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltBooking>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_BOOKING");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.BasicFare)
                    .HasColumnName("Basic_Fare")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.BookingDate)
                    .HasColumnName("Booking_Date")
                    .HasColumnType("date")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.BookingEditRemark).HasMaxLength(300);

                entity.Property(e => e.BookingId)
                    .HasColumnName("Booking_Id")
                    .HasMaxLength(20);

                entity.Property(e => e.BookingRemark)
                    .HasColumnName("Booking_Remark")
                    .HasMaxLength(500);

                entity.Property(e => e.BookingStatusId).HasColumnName("Booking_Status_Id");

                entity.Property(e => e.CarCategoryId).HasColumnName("Car_Category_Id");

                entity.Property(e => e.CarId).HasColumnName("Car_Id");

                entity.Property(e => e.CityFromId).HasColumnName("City_From_Id");

                entity.Property(e => e.CityToId).HasColumnName("City_To_Id");

                entity.Property(e => e.Completedropoffpaddress)
                    .HasColumnName("completedropoffpaddress")
                    .HasMaxLength(400);

                entity.Property(e => e.Completepickupaddress)
                    .HasColumnName("completepickupaddress")
                    .HasMaxLength(400);

                entity.Property(e => e.CouponCode)
                    .HasColumnName("Coupon_Code")
                    .HasMaxLength(50);

                entity.Property(e => e.CouponDiscount)
                    .HasColumnName("Coupon_Discount")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.CreatedBy).HasColumnName("Created_By");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.Distance).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.DriverCharge)
                    .HasColumnName("Driver_Charge")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.DriverId).HasColumnName("driver_id");

                entity.Property(e => e.DropOffAddress)
                    .HasColumnName("DropOff_Address")
                    .HasMaxLength(200);

                entity.Property(e => e.Duration).HasMaxLength(50);

                entity.Property(e => e.Fare).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Gst)
                    .HasColumnName("GST")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.GstFare)
                    .HasColumnName("GST_Fare")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.InvoiceDate)
                    .HasColumnName("Invoice_Date")
                    .HasColumnType("date");

                entity.Property(e => e.InvoiceNo)
                    .HasColumnName("Invoice_No")
                    .HasMaxLength(20);

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.MailId)
                    .HasColumnName("Mail_Id")
                    .HasMaxLength(50);

                entity.Property(e => e.MobileNo1)
                    .HasColumnName("Mobile_No1")
                    .HasMaxLength(50);

                entity.Property(e => e.MobileNo2)
                    .HasColumnName("Mobile_No2")
                    .HasMaxLength(50);

                entity.Property(e => e.ModeOfPaymentId).HasColumnName("Mode_Of_Payment_Id");

                entity.Property(e => e.Name).HasMaxLength(50);

                entity.Property(e => e.PickUpAddress)
                    .HasColumnName("PickUp_Address")
                    .HasMaxLength(200);

                entity.Property(e => e.PickUpAddressLatitude).HasMaxLength(100);

                entity.Property(e => e.PickUpAddressLongitude).HasMaxLength(100);

                entity.Property(e => e.PickUpDate)
                    .HasColumnName("PickUp_Date")
                    .HasColumnType("date");

                entity.Property(e => e.PickUpTime)
                    .HasColumnName("PickUp_Time")
                    .HasMaxLength(50);

                entity.Property(e => e.RazorpayOrderId)
                    .HasColumnName("razorpay_order_id")
                    .HasMaxLength(50);

                entity.Property(e => e.RazorpayPaymentId)
                    .HasColumnName("razorpay_payment_id")
                    .HasMaxLength(50);

                entity.Property(e => e.RazorpaySignature)
                    .HasColumnName("razorpay_signature")
                    .HasMaxLength(500);

                entity.Property(e => e.RazorpayStatus)
                    .HasColumnName("razorpay_status")
                    .HasMaxLength(50);

                entity.Property(e => e.TollCharge)
                    .HasColumnName("Toll_Charge")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.TripTypeId).HasColumnName("Trip_Type_Id");

                entity.Property(e => e.UpdatedBy).HasColumnName("Updated_By");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.VendorId).HasColumnName("Vendor_Id");
            });

            modelBuilder.Entity<RltBookingFare>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_BOOKING_FARE");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.BasicFare)
                    .HasColumnName("Basic_Fare")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.CarCategoryId).HasColumnName("Car_Category_Id");

                entity.Property(e => e.CityFrom).HasColumnName("City_From");

                entity.Property(e => e.CityTo).HasColumnName("City_To");

                entity.Property(e => e.CreatedBy).HasColumnName("Created_By");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.DriverCharge)
                    .HasColumnName("Driver_Charge")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.FinalFare)
                    .HasColumnName("Final_Fare")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Gst)
                    .HasColumnName("GST")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.GstAmount)
                    .HasColumnName("GST_Amount")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.Remark).HasMaxLength(500);

                entity.Property(e => e.TollCharge)
                    .HasColumnName("Toll_Charge")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.TotalFare)
                    .HasColumnName("Total_Fare")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.TripTypeId).HasColumnName("Trip_Type_Id");

                entity.Property(e => e.UpdatedBy).HasColumnName("Updated_By");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltBookingGst>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_BOOKING_GST");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.Cgst)
                    .HasColumnName("CGST")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.CreatedBy).HasColumnName("Created_By");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.Gst)
                    .HasColumnName("GST")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.Sgst)
                    .HasColumnName("SGST")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.UpdatedBy).HasColumnName("Updated_By");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltBookingHistrory>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("RLT_BOOKING_Histrory");

                entity.Property(e => e.BasicFare)
                    .HasColumnName("Basic_Fare")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.BookingDate)
                    .HasColumnName("Booking_Date")
                    .HasColumnType("date");

                entity.Property(e => e.BookingEditRemark).HasMaxLength(300);

                entity.Property(e => e.BookingId)
                    .HasColumnName("Booking_Id")
                    .HasMaxLength(20);

                entity.Property(e => e.BookingRemark)
                    .HasColumnName("Booking_Remark")
                    .HasMaxLength(500);

                entity.Property(e => e.BookingStatusId).HasColumnName("Booking_Status_Id");

                entity.Property(e => e.CarCategoryId).HasColumnName("Car_Category_Id");

                entity.Property(e => e.CarId).HasColumnName("Car_Id");

                entity.Property(e => e.CityFromId).HasColumnName("City_From_Id");

                entity.Property(e => e.CityToId).HasColumnName("City_To_Id");

                entity.Property(e => e.Completedropoffpaddress)
                    .HasColumnName("completedropoffpaddress")
                    .HasMaxLength(400);

                entity.Property(e => e.Completepickupaddress)
                    .HasColumnName("completepickupaddress")
                    .HasMaxLength(400);

                entity.Property(e => e.CouponCode)
                    .HasColumnName("Coupon_Code")
                    .HasMaxLength(50);

                entity.Property(e => e.CouponDiscount)
                    .HasColumnName("Coupon_Discount")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.CreatedBy).HasColumnName("Created_By");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.Distance).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.DriverCharge)
                    .HasColumnName("Driver_Charge")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.DriverId).HasColumnName("driver_id");

                entity.Property(e => e.DropOffAddress)
                    .HasColumnName("DropOff_Address")
                    .HasMaxLength(200);

                entity.Property(e => e.Duration).HasMaxLength(50);

                entity.Property(e => e.Fare).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Gst)
                    .HasColumnName("GST")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.GstFare)
                    .HasColumnName("GST_Fare")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.InvoiceDate)
                    .HasColumnName("Invoice_Date")
                    .HasColumnType("date");

                entity.Property(e => e.InvoiceNo)
                    .HasColumnName("Invoice_No")
                    .HasMaxLength(20);

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.MailId)
                    .HasColumnName("Mail_Id")
                    .HasMaxLength(50);

                entity.Property(e => e.MobileNo1)
                    .HasColumnName("Mobile_No1")
                    .HasMaxLength(50);

                entity.Property(e => e.MobileNo2)
                    .HasColumnName("Mobile_No2")
                    .HasMaxLength(50);

                entity.Property(e => e.ModeOfPaymentId).HasColumnName("Mode_Of_Payment_Id");

                entity.Property(e => e.Name).HasMaxLength(50);

                entity.Property(e => e.PickUpAddress)
                    .HasColumnName("PickUp_Address")
                    .HasMaxLength(200);

                entity.Property(e => e.PickUpAddressLatitude).HasMaxLength(100);

                entity.Property(e => e.PickUpAddressLongitude).HasMaxLength(100);

                entity.Property(e => e.PickUpDate)
                    .HasColumnName("PickUp_Date")
                    .HasColumnType("date");

                entity.Property(e => e.PickUpTime)
                    .HasColumnName("PickUp_Time")
                    .HasMaxLength(50);

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.RazorpayOrderId)
                    .HasColumnName("razorpay_order_id")
                    .HasMaxLength(50);

                entity.Property(e => e.RazorpayPaymentId)
                    .HasColumnName("razorpay_payment_id")
                    .HasMaxLength(50);

                entity.Property(e => e.RazorpaySignature)
                    .HasColumnName("razorpay_signature")
                    .HasMaxLength(500);

                entity.Property(e => e.RazorpayStatus)
                    .HasColumnName("razorpay_status")
                    .HasMaxLength(50);

                entity.Property(e => e.TollCharge)
                    .HasColumnName("Toll_Charge")
                    .HasColumnType("decimal(18, 2)");

                entity.Property(e => e.TripTypeId).HasColumnName("Trip_Type_Id");

                entity.Property(e => e.UpdatedBy).HasColumnName("Updated_By");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.VendorId).HasColumnName("Vendor_Id");
            });

            modelBuilder.Entity<RltBookingMode>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_BOOKING_MODE");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.BookingModeName)
                    .HasColumnName("Booking_Mode_Name")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.CounrtyAbbr)
                    .HasColumnName("Counrty_Abbr")
                    .HasMaxLength(5)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.LastModifiedBy).HasColumnName("Last_Modified_By");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltBookingPaymentDetails>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_BOOKING_PAYMENT_DETAILS");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.BaseFare).HasColumnName("Base_Fare");

                entity.Property(e => e.BookingLoginPkid).HasColumnName("Booking_Login_PKID");

                entity.Property(e => e.DiscountCoupon)
                    .HasColumnName("Discount_Coupon")
                    .HasMaxLength(50);

                entity.Property(e => e.DriverAllowance).HasColumnName("Driver_Allowance");

                entity.Property(e => e.PassengerAltPhone)
                    .HasColumnName("Passenger_Alt_Phone")
                    .HasMaxLength(15);

                entity.Property(e => e.PassengerDropoffAddress)
                    .HasColumnName("Passenger_Dropoff_Address")
                    .HasMaxLength(250);

                entity.Property(e => e.PassengerDropoffCityPkid).HasColumnName("Passenger_Dropoff_City_PKID");

                entity.Property(e => e.PassengerEmail)
                    .HasColumnName("Passenger_Email")
                    .HasMaxLength(50);

                entity.Property(e => e.PassengerName)
                    .HasColumnName("Passenger_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.PassengerPhone)
                    .HasColumnName("Passenger_Phone")
                    .HasMaxLength(15);

                entity.Property(e => e.PassengerPickupAddress)
                    .HasColumnName("Passenger_Pickup_Address")
                    .HasMaxLength(250);

                entity.Property(e => e.PassengerPickupCityPkid).HasColumnName("Passenger_Pickup_City_PKID");

                entity.Property(e => e.PassengerSpecialRequestComment)
                    .HasColumnName("Passenger_Special_Request_Comment")
                    .HasMaxLength(150);

                entity.Property(e => e.PassengerTripPurpose)
                    .HasColumnName("Passenger_Trip_Purpose")
                    .HasMaxLength(15);

                entity.Property(e => e.PassengerTripTypePkid).HasColumnName("Passenger_Trip_Type_PKID");

                entity.Property(e => e.PaymentAmountRecieved).HasColumnName("Payment_Amount_Recieved");

                entity.Property(e => e.PaymentIssueComment)
                    .HasColumnName("Payment_Issue_Comment")
                    .HasMaxLength(250);

                entity.Property(e => e.PaymentModePkid).HasColumnName("Payment_Mode_PKID");

                entity.Property(e => e.PaymentNeedToCollect).HasColumnName("Payment_Need_to_Collect");

                entity.Property(e => e.PaymentTransactionId)
                    .HasColumnName("Payment_TransactionId")
                    .HasMaxLength(50);

                entity.Property(e => e.PaymentTransactionStatus)
                    .HasColumnName("Payment_Transaction_Status")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.ServiceTax).HasColumnName("Service_Tax");

                entity.Property(e => e.TotalBookingAmount).HasColumnName("Total_Booking_Amount");

                entity.HasOne(d => d.BookingLoginPk)
                    .WithMany(p => p.RltBookingPaymentDetails)
                    .HasForeignKey(d => d.BookingLoginPkid)
                    .HasConstraintName("FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_USER_LOGIN_DETAILS");

                entity.HasOne(d => d.PassengerDropoffCityPk)
                    .WithMany(p => p.RltBookingPaymentDetailsPassengerDropoffCityPk)
                    .HasForeignKey(d => d.PassengerDropoffCityPkid)
                    .HasConstraintName("FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_Droppoff");

                entity.HasOne(d => d.PassengerPickupCityPk)
                    .WithMany(p => p.RltBookingPaymentDetailsPassengerPickupCityPk)
                    .HasForeignKey(d => d.PassengerPickupCityPkid)
                    .HasConstraintName("FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_CITY_PICKUP");

                entity.HasOne(d => d.PassengerTripTypePk)
                    .WithMany(p => p.RltBookingPaymentDetails)
                    .HasForeignKey(d => d.PassengerTripTypePkid)
                    .HasConstraintName("FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_TRIP_TYPES");

                entity.HasOne(d => d.PaymentModePk)
                    .WithMany(p => p.RltBookingPaymentDetails)
                    .HasForeignKey(d => d.PaymentModePkid)
                    .HasConstraintName("FK_RLT_BOOKING_PAYMENT_DETAILS_RLT_PAYMENT_METHOD");
            });

            modelBuilder.Entity<RltBookingRules>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_BOOKING_RULES");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CarCategoryId).HasColumnName("Car_Category_Id");

                entity.Property(e => e.CreatedBy).HasColumnName("Created_By");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.DistanceFrom).HasColumnName("Distance_From");

                entity.Property(e => e.DistanceTo).HasColumnName("Distance_To");

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.TripTypeId).HasColumnName("Trip_Type_Id");

                entity.Property(e => e.UpdatedBy).HasColumnName("Updated_By");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltBookingStatus>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_BOOKING_STATUS");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.BookingStatus)
                    .HasColumnName("BOOKING_STATUS")
                    .HasMaxLength(50);

                entity.Property(e => e.CreatedBy).HasColumnName("Created_By");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.IsActive)
                    .HasColumnName("Is_Active")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.ModifiedBy).HasColumnName("Modified_By");

                entity.Property(e => e.ModifiedDate)
                    .HasColumnName("Modified_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<Rlt_Bookings>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_BOOKINGS");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.BasicFare)
                    .HasColumnName("Basic_Fare")
                    .HasMaxLength(5);

                entity.Property(e => e.BookingDate)
                    .HasColumnName("Booking_Date")
                    .HasColumnType("date")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.BookingId)
                    .HasColumnName("Booking_Id")
                    .HasMaxLength(30);

                entity.Property(e => e.BookingRemark)
                    .HasColumnName("Booking_Remark")
                    .HasMaxLength(500);

                entity.Property(e => e.BookingSessionId)
                    .HasColumnName("BookingSessionID")
                    .HasMaxLength(30);

                entity.Property(e => e.BookingStatusId)
                    .HasColumnName("Booking_Status_Id")
                    .HasMaxLength(10);

                entity.Property(e => e.CarCategoryId)
                    .HasColumnName("Car_Category_Id")
                    .HasMaxLength(5);

                entity.Property(e => e.CarId)
                    .HasColumnName("Car_Id")
                    .HasMaxLength(10);

                entity.Property(e => e.CashToPayDriver).HasMaxLength(5);

                entity.Property(e => e.CityFromId)
                    .HasColumnName("City_From_Id")
                    .HasMaxLength(5);

                entity.Property(e => e.CityToId)
                    .HasColumnName("City_To_Id")
                    .HasMaxLength(5);

                entity.Property(e => e.CouponCode)
                    .HasColumnName("Coupon_Code")
                    .HasMaxLength(50);

                entity.Property(e => e.CouponDiscount)
                    .HasColumnName("Coupon_Discount")
                    .HasMaxLength(30);

                entity.Property(e => e.CreatedBy)
                    .HasColumnName("Created_By")
                    .HasMaxLength(10);

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.Distance).HasMaxLength(5);

                entity.Property(e => e.DriverCharge)
                    .HasColumnName("Driver_Charge")
                    .HasMaxLength(5);

                entity.Property(e => e.DriverNightCharge).HasMaxLength(5);

                entity.Property(e => e.DropOffAddress)
                    .HasColumnName("DropOff_Address")
                    .HasMaxLength(200);

                entity.Property(e => e.DropOffLatLong).HasMaxLength(200);

                entity.Property(e => e.Duration).HasMaxLength(50);

                entity.Property(e => e.Fare).HasMaxLength(10);

                entity.Property(e => e.Gst)
                    .HasColumnName("GST")
                    .HasMaxLength(5);

                entity.Property(e => e.GstFare)
                    .HasColumnName("GST_Fare")
                    .HasMaxLength(10);

                entity.Property(e => e.InvoiceDate)
                    .HasColumnName("Invoice_Date")
                    .HasColumnType("date");

                entity.Property(e => e.InvoiceNo)
                    .HasColumnName("Invoice_No")
                    .HasMaxLength(20);

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.IsWhatsAppNumber).HasDefaultValueSql("((0))");

                entity.Property(e => e.PaymentMode).HasMaxLength(30);

                entity.Property(e => e.PaymentOption).HasMaxLength(30);

                entity.Property(e => e.PickUpAddress)
                    .HasColumnName("PickUp_Address")
                    .HasMaxLength(200);

                entity.Property(e => e.PickUpDate)
                    .HasColumnName("PickUp_Date")
                    .HasMaxLength(30);

                entity.Property(e => e.PickUpLatLong).HasMaxLength(200);

                entity.Property(e => e.PickUpTime)
                    .HasColumnName("PickUp_Time")
                    .HasMaxLength(50);

                entity.Property(e => e.RazorpayOrderId)
                    .HasColumnName("razorpay_order_id")
                    .HasMaxLength(50);

                entity.Property(e => e.RazorpayPaymentId)
                    .HasColumnName("razorpay_payment_id")
                    .HasMaxLength(50);

                entity.Property(e => e.RazorpaySignature)
                    .HasColumnName("razorpay_signature")
                    .HasMaxLength(500);

                entity.Property(e => e.RazorpayStatus)
                    .HasColumnName("razorpay_status")
                    .HasMaxLength(50);

                entity.Property(e => e.TollCharge)
                    .HasColumnName("Toll_Charge")
                    .HasMaxLength(10);

                entity.Property(e => e.TravelerEmail).HasMaxLength(30);

                entity.Property(e => e.TravelerName).HasMaxLength(200);

                entity.Property(e => e.TravelerPhone).HasMaxLength(30);

                entity.Property(e => e.TripTypeId)
                    .HasColumnName("Trip_Type_Id")
                    .HasMaxLength(5);

                entity.Property(e => e.UpdatedBy)
                    .HasColumnName("Updated_By")
                    .HasMaxLength(10);

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.VendorId)
                    .HasColumnName("Vendor_Id")
                    .HasMaxLength(10);
            });

            modelBuilder.Entity<RltCarCategory>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_CAR_CATEGORY");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CarCategoryAbbr)
                    .HasColumnName("Car_Category_Abbr")
                    .HasMaxLength(60);

                entity.Property(e => e.CarCategoryName)
                    .HasColumnName("Car_Category_Name")
                    .HasMaxLength(30);

                entity.Property(e => e.CarCategroyImage)
                    .HasColumnName("Car_Categroy_Image")
                    .HasMaxLength(150);

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.Features).HasMaxLength(500);

                entity.Property(e => e.IsActive)
                    .HasColumnName("Is_Active")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.PerKmFare).HasColumnName("Per_KM_fare");

                entity.Property(e => e.PkGuid)
                    .HasColumnName("PK_GUID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltCarChargesFecilitiesDetails>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_CAR_CHARGES_FECILITIES_DETAILS");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CarCategoryPkid).HasColumnName("Car_Category_PKID");

                entity.Property(e => e.CarChargesPkm).HasColumnName("Car_Charges_PKM");

                entity.Property(e => e.CarDriverChargesPd).HasColumnName("Car_Driver_Charges_PD");

                entity.Property(e => e.CarWaitingChargesPm).HasColumnName("Car_Waiting_Charges_PM");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.ExtrasChargesComment)
                    .HasColumnName("Extras_Charges_Comment")
                    .HasMaxLength(100);

                entity.Property(e => e.InclusiveChargesComment)
                    .HasColumnName("Inclusive_Charges_Comment")
                    .HasMaxLength(100);

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.IsContainAc).HasColumnName("Is_Contain_AC");

                entity.Property(e => e.IsLuggageAllow).HasColumnName("Is_Luggage_Allow");

                entity.Property(e => e.IsPetAllow).HasColumnName("Is_Pet_Allow");

                entity.Property(e => e.IsSmokingAllow)
                    .HasColumnName("Is_Smoking_Allow")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.TotalNumberSeats)
                    .HasColumnName("Total_Number_Seats")
                    .HasMaxLength(10)
                    .IsFixedLength();

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");

                entity.HasOne(d => d.CarCategoryPk)
                    .WithMany(p => p.RltCarChargesFecilitiesDetails)
                    .HasForeignKey(d => d.CarCategoryPkid)
                    .HasConstraintName("FK_RLT_CAR_CHARGES_FECILITIES_DETAILS_RLT_CAR_CATEGORY");
            });

            modelBuilder.Entity<RltCarCompany>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_CAR_COMPANY");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CompanyName)
                    .HasColumnName("Company_Name")
                    .HasMaxLength(30);

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.PkGuid)
                    .HasColumnName("PK_GUID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltCarDetails>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_CAR_DETAILS");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CarCompanyPkid).HasColumnName("Car_Company_PKID");

                entity.Property(e => e.CarFuelTypeStatus)
                    .HasColumnName("Car_Fuel_Type_Status")
                    .HasMaxLength(50);

                entity.Property(e => e.CarManufacturingYear)
                    .HasColumnName("Car_Manufacturing_Year")
                    .HasMaxLength(10);

                entity.Property(e => e.CarModelPkid).HasColumnName("Car_Model_PKID");

                entity.Property(e => e.CarNumber)
                    .HasColumnName("Car_Number")
                    .HasMaxLength(20);

                entity.Property(e => e.CarOwnerPkid).HasColumnName("Car_Owner_PKID");

                entity.Property(e => e.CarPurchaseYear)
                    .HasColumnName("Car_Purchase_Year")
                    .HasMaxLength(10);

                entity.Property(e => e.CarRegisteredCityPkid).HasColumnName("Car_Registered_City_PKID");

                entity.Property(e => e.CarRegisteredDocument)
                    .HasColumnName("Car_Registered_Document")
                    .HasMaxLength(300);

                entity.Property(e => e.CreatedBy).HasColumnName("Created_BY");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.IsActive)
                    .HasColumnName("Is_Active")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.PkGuid)
                    .HasColumnName("PK_GUID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.UpdateDate)
                    .HasColumnName("Update_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.UpdatedBy).HasColumnName("Updated_BY");

                entity.Property(e => e.VendorPkid).HasColumnName("Vendor_PKID");
            });

            modelBuilder.Entity<RltCarDocs>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_CAR_DOCS");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CarDocEndDate)
                    .HasColumnName("Car_Doc_EndDate")
                    .HasColumnType("date");

                entity.Property(e => e.CarDocId).HasColumnName("Car_Doc_Id");

                entity.Property(e => e.CarPkid).HasColumnName("Car_PKID");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.DocumentPath)
                    .HasColumnName("Document_Path")
                    .HasMaxLength(150);

                entity.Property(e => e.IsVerified).HasColumnName("Is_Verified");

                entity.Property(e => e.LastModifiedBy).HasColumnName("Last_Modified_By");

                entity.Property(e => e.LastModifiedDate)
                    .HasColumnName("Last_Modified_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltCarDriverDetails>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_CAR_DRIVER_DETAILS");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CarOwnerPkid).HasColumnName("Car_Owner_PKID");

                entity.Property(e => e.CarPkid).HasColumnName("Car_PKID");

                entity.Property(e => e.CreatedBy).HasColumnName("Created_BY");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.DrivePhone1)
                    .HasColumnName("Drive_Phone_1")
                    .HasMaxLength(15);

                entity.Property(e => e.DrivePhone2)
                    .HasColumnName("Drive_Phone_2")
                    .HasMaxLength(10)
                    .IsFixedLength();

                entity.Property(e => e.DriverAadhaar)
                    .HasColumnName("Driver_Aadhaar")
                    .HasMaxLength(150);

                entity.Property(e => e.DriverCurrentAddress)
                    .HasColumnName("Driver_Current_Address")
                    .HasMaxLength(250);

                entity.Property(e => e.DriverCurrentAddress1)
                    .HasColumnName("Driver_Current_Address1")
                    .HasMaxLength(250);

                entity.Property(e => e.DriverCurrentAddress2)
                    .HasColumnName("Driver_Current_Address2")
                    .HasMaxLength(250);

                entity.Property(e => e.DriverCurrentAddressCity).HasColumnName("Driver_Current_Address_City");

                entity.Property(e => e.DriverCurrentAddressState).HasColumnName("Driver_Current_Address_State");

                entity.Property(e => e.DriverCurrentPermanentSame).HasColumnName("Driver_Current_Permanent_Same");

                entity.Property(e => e.DriverDl)
                    .HasColumnName("Driver_DL")
                    .HasMaxLength(150);

                entity.Property(e => e.DriverDob)
                    .HasColumnName("Driver_DOB")
                    .HasColumnType("datetime");

                entity.Property(e => e.DriverDrinkingStatus)
                    .HasColumnName("Driver_Drinking_Status")
                    .HasMaxLength(25)
                    .IsUnicode(false);

                entity.Property(e => e.DriverEatingType)
                    .HasColumnName("Driver_Eating_Type")
                    .HasMaxLength(25)
                    .IsUnicode(false);

                entity.Property(e => e.DriverEmail)
                    .HasColumnName("Driver_Email")
                    .HasMaxLength(100);

                entity.Property(e => e.DriverFatherName)
                    .HasColumnName("Driver_Father_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.DriverFname)
                    .HasColumnName("Driver_FName")
                    .HasMaxLength(20);

                entity.Property(e => e.DriverGender)
                    .HasColumnName("Driver_Gender")
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.DriverLname)
                    .HasColumnName("Driver_LName")
                    .HasMaxLength(20);

                entity.Property(e => e.DriverMarritalStatus)
                    .HasColumnName("Driver_Marrital_Status")
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.DriverMname)
                    .HasColumnName("Driver_MName")
                    .HasMaxLength(20);

                entity.Property(e => e.DriverMotherName)
                    .HasColumnName("Driver_Mother_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.DriverName)
                    .HasColumnName("Driver_Name")
                    .HasMaxLength(75);

                entity.Property(e => e.DriverPermanentAddress)
                    .HasColumnName("Driver_Permanent_Address")
                    .HasMaxLength(250);

                entity.Property(e => e.DriverPermanentAddress1)
                    .HasColumnName("Driver_Permanent_Address1")
                    .HasMaxLength(250);

                entity.Property(e => e.DriverPermanentAddress2)
                    .HasColumnName("Driver_Permanent_Address2")
                    .HasMaxLength(250);

                entity.Property(e => e.DriverPermanentAddressCity).HasColumnName("Driver_Permanent_Address_City");

                entity.Property(e => e.DriverPermanentAddressState).HasColumnName("Driver_Permanent_Address_State");

                entity.Property(e => e.DriverPhoto)
                    .HasColumnName("Driver_Photo")
                    .HasMaxLength(150);

                entity.Property(e => e.DriverPwd)
                    .HasColumnName("Driver_Pwd")
                    .HasMaxLength(100);

                entity.Property(e => e.DriverReligion)
                    .HasColumnName("Driver_Religion")
                    .HasMaxLength(20);

                entity.Property(e => e.DriverSmokingStatus)
                    .HasColumnName("Driver_Smoking_Status")
                    .HasMaxLength(25)
                    .IsUnicode(false);

                entity.Property(e => e.IsActive)
                    .HasColumnName("Is_Active")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.Otpnumber).HasColumnName("OTPNumber");

                entity.Property(e => e.OtpupdatedDateTime)
                    .HasColumnName("OTPUpdatedDateTime")
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Phone1IsWhatsup)
                    .HasColumnName("Phone_1_IsWhatsup")
                    .HasMaxLength(10);

                entity.Property(e => e.PkGuid).HasColumnName("PK_GUID");

                entity.Property(e => e.SpecialRemarking)
                    .HasColumnName("Special_Remarking")
                    .HasMaxLength(200);

                entity.Property(e => e.UpdatedBy).HasColumnName("Updated_BY");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.UserId)
                    .HasColumnName("UserID")
                    .HasMaxLength(50);

                entity.Property(e => e.VendorPkid).HasColumnName("Vendor_PKID");

                entity.Property(e => e.WillOwnerDriveCarStatus)
                    .HasColumnName("Will_Owner_Drive_Car_Status")
                    .HasMaxLength(10)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<RltCarDriverDocs>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_CAR_DRIVER_DOCS");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CarDriverDocEndDate)
                    .HasColumnName("CarDriver_Doc_EndDate")
                    .HasColumnType("date");

                entity.Property(e => e.CarDriverDocId).HasColumnName("CarDriver_Doc_Id");

                entity.Property(e => e.CarDriverPkid).HasColumnName("CarDriver_PKID");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.DocumentPath)
                    .HasColumnName("Document_Path")
                    .HasMaxLength(150);

                entity.Property(e => e.IsVerified).HasColumnName("Is_Verified");

                entity.Property(e => e.LastModifiedBy).HasColumnName("Last_Modified_By");

                entity.Property(e => e.LastModifiedDate)
                    .HasColumnName("Last_Modified_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltCarFuelTypes>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_CAR_FUEL_TYPES");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CarFuelType)
                    .HasColumnName("CAR_FUEL_TYPE")
                    .HasMaxLength(10);

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.PkGuid)
                    .HasColumnName("PK_GUID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltCarModel>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_CAR_MODEL");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CarCategoryPkid).HasColumnName("Car_Category_PKID");

                entity.Property(e => e.CarCompanyPkid).HasColumnName("Car_Company_PKID");

                entity.Property(e => e.CarFuelTypeStatus).HasColumnName("Car_Fuel_Type_Status");

                entity.Property(e => e.CarModelName)
                    .HasColumnName("Car_Model_Name")
                    .HasMaxLength(20);

                entity.Property(e => e.CarSegmentPkid).HasColumnName("Car_Segment_PKID");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.PkGuid)
                    .HasColumnName("PK_GUID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");

                entity.HasOne(d => d.CarCategoryPk)
                    .WithMany(p => p.RltCarModel)
                    .HasForeignKey(d => d.CarCategoryPkid)
                    .HasConstraintName("FK_RLT_CAR_MODEL_RLT_CAR_CATEGORY");

                entity.HasOne(d => d.CarCompanyPk)
                    .WithMany(p => p.RltCarModel)
                    .HasForeignKey(d => d.CarCompanyPkid)
                    .HasConstraintName("FK_RLT_CAR_MODEL_RLT_CAR_COMPANY");

                entity.HasOne(d => d.CarFuelTypeStatusNavigation)
                    .WithMany(p => p.RltCarModel)
                    .HasForeignKey(d => d.CarFuelTypeStatus)
                    .HasConstraintName("FK_RLT_CAR_MODEL_RLT_CAR_FUEL_TYPES");
            });

            modelBuilder.Entity<RltCarOwnerBankDetails>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_CAR_OWNER_BANK_DETAILS");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.BankAccBranchAddress)
                    .HasColumnName("Bank_Acc_Branch_Address")
                    .HasMaxLength(150);

                entity.Property(e => e.BankIfscCode)
                    .HasColumnName("Bank_IFSC_Code")
                    .HasMaxLength(15);

                entity.Property(e => e.BankNamePkid).HasColumnName("Bank_Name_PKID");

                entity.Property(e => e.CarOwnerBankAccName)
                    .HasColumnName("Car_Owner_Bank_Acc_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.CarOwnerBankAccNumber)
                    .HasColumnName("Car_Owner_Bank_Acc_Number")
                    .HasMaxLength(15);

                entity.Property(e => e.CarOwnerBankCancelledChkPhoto)
                    .HasColumnName("Car_Owner_Bank_Cancelled_Chk_Photo")
                    .HasMaxLength(150);

                entity.Property(e => e.CarOwnerBankRegisteredPhone)
                    .HasColumnName("Car_Owner_Bank_Registered_Phone")
                    .HasMaxLength(15);

                entity.Property(e => e.CarOwnerPkid).HasColumnName("Car_Owner_PKID");

                entity.Property(e => e.CreatdeDate)
                    .HasColumnName("Creatde_Date")
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.CreatedBy).HasColumnName("Created_BY");

                entity.Property(e => e.IsActive)
                    .HasColumnName("Is_Active")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.PkGuid)
                    .HasColumnName("PK_GUID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.UpdatedBy).HasColumnName("Updated_BY");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.UpdaterComment)
                    .HasColumnName("Updater_Comment")
                    .HasMaxLength(150);

                entity.HasOne(d => d.BankNamePk)
                    .WithMany(p => p.RltCarOwnerBankDetails)
                    .HasForeignKey(d => d.BankNamePkid)
                    .HasConstraintName("FK_RLT_CAR_OWNER_BANK_DETAILS_RLT_BANK_NAMES");
            });

            modelBuilder.Entity<RltCarOwnerDetails>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_CAR_OWNER_DETAILS");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CarOwnerAadhaar)
                    .HasColumnName("Car_Owner_Aadhaar")
                    .HasMaxLength(50);

                entity.Property(e => e.CarOwnerAddress)
                    .HasColumnName("Car_Owner_Address")
                    .HasMaxLength(500);

                entity.Property(e => e.CarOwnerDl)
                    .HasColumnName("Car_Owner_DL")
                    .HasMaxLength(50);

                entity.Property(e => e.CarOwnerEmail)
                    .HasColumnName("Car_Owner_Email")
                    .HasMaxLength(50);

                entity.Property(e => e.CarOwnerName)
                    .HasColumnName("Car_Owner_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.CarOwnerPhone1)
                    .HasColumnName("Car_Owner_Phone1")
                    .HasMaxLength(50);

                entity.Property(e => e.CarOwnerPhone2)
                    .HasColumnName("Car_Owner_Phone2")
                    .HasMaxLength(50);

                entity.Property(e => e.CarOwnerPhoto)
                    .HasColumnName("Car_Owner_Photo")
                    .HasMaxLength(500);

                entity.Property(e => e.CreatedBy).HasColumnName("Created_BY");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.Phone1IsWhatsup)
                    .HasColumnName("Phone1_IsWhatsup")
                    .HasMaxLength(10);

                entity.Property(e => e.UpdateDate)
                    .HasColumnName("Update_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.UpdatedBy).HasColumnName("Updated_BY");

                entity.Property(e => e.VendorPkid).HasColumnName("Vendor_PKID");
            });

            modelBuilder.Entity<RltCarSegment>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_CAR_SEGMENT");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CarSegment)
                    .HasColumnName("Car_Segment")
                    .HasMaxLength(100);

                entity.Property(e => e.CarSegmentDescription)
                    .HasColumnName("Car_Segment_Description")
                    .HasMaxLength(500);

                entity.Property(e => e.Features)
                    .HasColumnName("features")
                    .HasMaxLength(500);

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.PerKmFare).HasColumnName("Per_KM_fare");
            });

            modelBuilder.Entity<RltCity>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_CITY");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CityAbbr)
                    .HasColumnName("City_Abbr")
                    .HasMaxLength(5)
                    .IsUnicode(false);

                entity.Property(e => e.CityName)
                    .HasColumnName("City_Name")
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.ELoc)
                    .HasColumnName("eLoc")
                    .HasMaxLength(50);

                entity.Property(e => e.IsActive)
                    .HasColumnName("Is_Active")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.Latitude)
                    .HasColumnName("latitude")
                    .HasMaxLength(50);

                entity.Property(e => e.Longitude)
                    .HasColumnName("longitude")
                    .HasMaxLength(50);

                entity.Property(e => e.OrderIndex).HasColumnName("orderIndex");

                entity.Property(e => e.PkGuid)
                    .HasColumnName("PK_GUID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.Score)
                    .HasColumnName("score")
                    .HasMaxLength(50);

                entity.Property(e => e.StatePkid).HasColumnName("State_PKID");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");

                entity.HasOne(d => d.StatePk)
                    .WithMany(p => p.RltCity)
                    .HasForeignKey(d => d.StatePkid)
                    .HasConstraintName("FK_RLT_CITY_RLT_STATE");
            });

            modelBuilder.Entity<RltClassMaster>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_CLASS_MASTER");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.ClassChargesPkms).HasColumnName("Class_Charges_PKMs");

                entity.Property(e => e.ClassName)
                    .HasColumnName("Class_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.DriverNightCharges).HasColumnName("Driver_Night_Charges");

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.OtherCharges).HasColumnName("Other_Charges");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.WaitingCharges).HasColumnName("Waiting_Charges");
            });

            modelBuilder.Entity<RltCompanyDetails>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_Company_Details");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CompanyAddress)
                    .HasColumnName("Company_Address")
                    .HasMaxLength(500);

                entity.Property(e => e.CompanyName)
                    .HasColumnName("Company_Name")
                    .HasMaxLength(500);

                entity.Property(e => e.SacCode)
                    .HasColumnName("SAC_Code")
                    .HasMaxLength(50);

                entity.Property(e => e.StateGstin)
                    .HasColumnName("State_GSTIN")
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<RltContactEnquiries>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_CONTACT_ENQUIRIES");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.EmailId)
                    .HasColumnName("EMailID")
                    .HasMaxLength(30);

                entity.Property(e => e.EnquiryMessage).HasMaxLength(300);

                entity.Property(e => e.IsWhatsAppNumber).HasDefaultValueSql("((0))");

                entity.Property(e => e.Name).HasMaxLength(30);

                entity.Property(e => e.PhoneNumber).HasMaxLength(15);

                entity.Property(e => e.RequestDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<RltContactInformation>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_ContactInformation");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.Address).HasMaxLength(350);

                entity.Property(e => e.BusinessEmailNo).HasMaxLength(200);

                entity.Property(e => e.BusinessTelNo).HasMaxLength(170);

                entity.Property(e => e.CreateDate)
                    .HasColumnType("date")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Fblink)
                    .HasColumnName("FBLink")
                    .HasMaxLength(200);

                entity.Property(e => e.GooglePlusLink).HasMaxLength(200);

                entity.Property(e => e.GpscordinationLatitude)
                    .HasColumnName("GPSCordinationLatitude")
                    .HasMaxLength(50);

                entity.Property(e => e.GpscordinationLongitude)
                    .HasColumnName("GPSCordinationLongitude")
                    .HasMaxLength(50);

                entity.Property(e => e.InstagramLink).HasMaxLength(200);

                entity.Property(e => e.IsActive)
                    .HasColumnName("isActive")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.PinsterLink).HasMaxLength(200);

                entity.Property(e => e.SalesEmailNo).HasMaxLength(200);

                entity.Property(e => e.SalesTelNo).HasMaxLength(170);

                entity.Property(e => e.TwitterLink).HasMaxLength(200);

                entity.Property(e => e.WorkingHours).HasMaxLength(250);
            });

            modelBuilder.Entity<RltCountry>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_COUNTRY");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CounrtyAbbr)
                    .HasColumnName("Counrty_Abbr")
                    .HasMaxLength(5)
                    .IsUnicode(false);

                entity.Property(e => e.CountryName)
                    .HasColumnName("Country_Name")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.PkGuid)
                    .HasColumnName("PK_GUID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltDepartmentName>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_Department_Name");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.DepartmentName).HasMaxLength(50);
            });

            modelBuilder.Entity<RltDiscountCoupon>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_DISCOUNT_COUPON");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CouponLastDate)
                    .HasColumnName("Coupon_Last_Date")
                    .HasColumnType("date");

                entity.Property(e => e.CouponName)
                    .HasColumnName("Coupon_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.CouponRemark)
                    .HasColumnName("Coupon_Remark")
                    .HasMaxLength(500);

                entity.Property(e => e.CreatedBy).HasColumnName("Created_By");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.Discount).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.DiscountCoupon)
                    .HasColumnName("Discount_Coupon")
                    .HasMaxLength(50);

                entity.Property(e => e.FareWhenApplied).HasColumnName("Fare_When_Applied");

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.MaxDiscount).HasColumnName("Max_Discount");

                entity.Property(e => e.UpdatedBy).HasColumnName("Updated_By");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltDocumnetsName>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_DOCUMNETS_NAME");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DocFor)
                    .HasColumnName("Doc_For")
                    .HasMaxLength(50);

                entity.Property(e => e.DocName)
                    .HasColumnName("Doc_Name")
                    .HasMaxLength(30);

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.IsDocExpiryDate).HasColumnName("Is_Doc_Expiry_Date");

                entity.Property(e => e.IsDocReq).HasColumnName("Is_Doc_Req");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltDriverApproveStatus>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_DRIVER_APPROVE_STATUS");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CreatedBy).HasColumnName("Created_By");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.IsActive)
                    .HasColumnName("Is_Active")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.ModifiedBy).HasColumnName("Modified_By");

                entity.Property(e => e.ModifiedDate)
                    .HasColumnName("Modified_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.Status).HasMaxLength(50);
            });

            modelBuilder.Entity<RltDriverEnquiries>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_DRIVER_ENQUIRIES");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CreatedBy).HasColumnName("Created_BY");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.DriverAddress)
                    .HasColumnName("Driver_Address")
                    .HasMaxLength(500);

                entity.Property(e => e.DriverApprovingStatus).HasColumnName("Driver_Approving_Status");

                entity.Property(e => e.DriverCarNo)
                    .HasColumnName("Driver_Car_No")
                    .HasMaxLength(20);

                entity.Property(e => e.DriverMail)
                    .HasColumnName("Driver_Mail")
                    .HasMaxLength(50);

                entity.Property(e => e.DriverName)
                    .HasColumnName("Driver_Name")
                    .HasMaxLength(150);

                entity.Property(e => e.DriverPhone1)
                    .HasColumnName("Driver_Phone_1")
                    .HasMaxLength(15);

                entity.Property(e => e.IsActive)
                    .HasColumnName("Is_Active")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.IsWhatsAppNumber).HasDefaultValueSql("((0))");

                entity.Property(e => e.RequestDate).HasColumnType("datetime");

                entity.Property(e => e.UpdatedBy).HasColumnName("Updated_BY");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltEmployee>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_Employee");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CreatedBy).HasColumnName("Created_BY");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.CurrentAddress)
                    .HasColumnName("Current_Address")
                    .HasMaxLength(250);

                entity.Property(e => e.CurrentAddress1)
                    .HasColumnName("Current_Address1")
                    .HasMaxLength(250);

                entity.Property(e => e.CurrentAddress2)
                    .HasColumnName("Current_Address2")
                    .HasMaxLength(250);

                entity.Property(e => e.CurrentAddressCity).HasColumnName("Current_Address_City");

                entity.Property(e => e.CurrentAddressState).HasColumnName("Current_Address_State");

                entity.Property(e => e.CurrentPermanentSame).HasColumnName("Current_Permanent_Same");

                entity.Property(e => e.DegreeCollege)
                    .HasColumnName("Degree_College")
                    .HasMaxLength(50);

                entity.Property(e => e.DegreeName)
                    .HasColumnName("Degree_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.DegreePassingYear)
                    .HasColumnName("Degree_PassingYear")
                    .HasMaxLength(50);

                entity.Property(e => e.DegreePercentage)
                    .HasColumnName("Degree_Percentage")
                    .HasMaxLength(10);

                entity.Property(e => e.Department).HasMaxLength(50);

                entity.Property(e => e.Designation).HasMaxLength(50);

                entity.Property(e => e.Dob)
                    .HasColumnName("DOB")
                    .HasColumnType("datetime");

                entity.Property(e => e.Email).HasMaxLength(50);

                entity.Property(e => e.EmployeeId)
                    .HasColumnName("Employee_ID")
                    .HasMaxLength(50);

                entity.Property(e => e.EmployeeName)
                    .HasColumnName("Employee_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.FatherName)
                    .HasColumnName("Father_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.Gender).HasMaxLength(50);

                entity.Property(e => e.IsActive)
                    .HasColumnName("Is_Active")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.MarritalStatus)
                    .HasColumnName("Marrital_Status")
                    .HasMaxLength(50);

                entity.Property(e => e.MasterDegreeCollege)
                    .HasColumnName("Master_Degree_College")
                    .HasMaxLength(50);

                entity.Property(e => e.MasterDegreeName)
                    .HasColumnName("Master_Degree_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.MasterDegreePassingYear)
                    .HasColumnName("Master_Degree_PassingYear")
                    .HasMaxLength(50);

                entity.Property(e => e.MasterDegreePercentage)
                    .HasColumnName("Master_Degree_Percentage")
                    .HasMaxLength(10);

                entity.Property(e => e.MotherName)
                    .HasColumnName("Mother_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.PermanentAddress)
                    .HasColumnName("Permanent_Address")
                    .HasMaxLength(250);

                entity.Property(e => e.PermanentAddress1)
                    .HasColumnName("Permanent_Address1")
                    .HasMaxLength(250);

                entity.Property(e => e.PermanentAddress2)
                    .HasColumnName("Permanent_Address2")
                    .HasMaxLength(250);

                entity.Property(e => e.PermanentAddressCity).HasColumnName("Permanent_Address_City");

                entity.Property(e => e.PermanentAddressState).HasColumnName("Permanent_Address_State");

                entity.Property(e => e.Phone1)
                    .HasColumnName("Phone_1")
                    .HasMaxLength(20);

                entity.Property(e => e.Phone1IsWhatsup)
                    .HasColumnName("Phone_1_IsWhatsup")
                    .HasMaxLength(10);

                entity.Property(e => e.Phone2)
                    .HasColumnName("Phone_2")
                    .HasMaxLength(20);

                entity.Property(e => e.Photo).HasMaxLength(500);

                entity.Property(e => e.SpousesName)
                    .HasColumnName("Spouses_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.Supervisor).HasMaxLength(50);

                entity.Property(e => e.UpdatedBy).HasColumnName("Updated_BY");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.WorkCity).HasColumnName("Work_City");

                entity.Property(e => e.WorkEmail)
                    .HasColumnName("Work_Email")
                    .HasMaxLength(50);

                entity.Property(e => e.WorkPhone)
                    .HasColumnName("Work_Phone")
                    .HasMaxLength(20);

                entity.Property(e => e.WorkStartDate)
                    .HasColumnName("Work_StartDate")
                    .HasColumnType("date");

                entity.Property(e => e._10thPassingYear)
                    .HasColumnName("10th_PassingYear")
                    .HasMaxLength(50);

                entity.Property(e => e._10thPercentage)
                    .HasColumnName("10th_Percentage")
                    .HasMaxLength(10);

                entity.Property(e => e._10thSchool)
                    .HasColumnName("10th_School")
                    .HasMaxLength(50);

                entity.Property(e => e._12thPassingYear)
                    .HasColumnName("12th_PassingYear")
                    .HasMaxLength(50);

                entity.Property(e => e._12thPercentage)
                    .HasColumnName("12th_Percentage")
                    .HasMaxLength(10);

                entity.Property(e => e._12thSchool)
                    .HasColumnName("12th_School")
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<RltEntityGenerals>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_ENTITY_GENERALS");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.ActionDate)
                    .HasColumnName("Action_Date")
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.EntityActionCode).HasColumnName("Entity_Action_Code");

                entity.Property(e => e.EntityFlowCode).HasColumnName("Entity_Flow_Code");

                entity.Property(e => e.EntityFlowRunBy).HasColumnName("Entity_Flow_Run_By");
            });

            modelBuilder.Entity<RltFaqDetails>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_FAQ_Details");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.Faqdetails).HasColumnName("FAQDetails");
            });

            modelBuilder.Entity<RltLocationCode>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_LOCATION_CODE");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CityPkid).HasColumnName("City_PKID");

                entity.Property(e => e.CreatdedDate)
                    .HasColumnName("Creatded_Date")
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.IsActive)
                    .HasColumnName("Is_Active")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.LocationName)
                    .HasColumnName("Location_Name")
                    .HasMaxLength(150);

                entity.Property(e => e.PkGuid)
                    .HasColumnName("PK_GUID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.ZipCode)
                    .HasColumnName("ZIP_Code")
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.HasOne(d => d.CityPk)
                    .WithMany(p => p.RltLocationCode)
                    .HasForeignKey(d => d.CityPkid)
                    .HasConstraintName("FK_RLT_LOCATION_CODE_RLT_CITY");
            });

            modelBuilder.Entity<RltLog>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("RLT_LOG");

                entity.Property(e => e.Date).HasColumnType("datetime");

                entity.Property(e => e.Exception)
                    .HasMaxLength(2000)
                    .IsUnicode(false);

                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.Level)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Logger)
                    .IsRequired()
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.Message)
                    .IsRequired()
                    .HasMaxLength(4000)
                    .IsUnicode(false);

                entity.Property(e => e.Thread)
                    .IsRequired()
                    .HasMaxLength(255)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<RltMenuMaster>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_MENU_MASTER");

                entity.Property(e => e.Pkid)
                    .HasColumnName("PKID")
                    .ValueGeneratedNever();

                entity.Property(e => e.ActiveMenuClass).HasMaxLength(50);

                entity.Property(e => e.CreatedBy).HasMaxLength(50);

                entity.Property(e => e.CreatedDate)
                    .HasColumnType("date")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.MenuIcon).HasMaxLength(50);

                entity.Property(e => e.MenuName).HasMaxLength(50);

                entity.Property(e => e.MenuUrl)
                    .HasColumnName("MenuURL")
                    .HasMaxLength(150);

                entity.Property(e => e.PageId).HasMaxLength(150);
            });

            modelBuilder.Entity<RltNextId>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_NextId");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.Type).HasMaxLength(50);
            });

            modelBuilder.Entity<RltPaymentMethod>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_PAYMENT_METHOD");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.IsActive)
                    .HasColumnName("Is_Active")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.PaymentMethodName)
                    .HasColumnName("Payment_Method_Name")
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltPrivacyPolicy>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_PrivacyPolicy");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.PrivacyPolicy).HasColumnType("text");
            });

            modelBuilder.Entity<RltRoleMenuMapping>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_ROLE_MENU_MAPPING");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CreatedBy).HasMaxLength(75);

                entity.Property(e => e.CreatedDate).HasColumnType("datetime");

                entity.Property(e => e.IsAdd).HasColumnName("Is_Add");

                entity.Property(e => e.IsDelete).HasColumnName("Is_Delete");

                entity.Property(e => e.IsEdit).HasColumnName("Is_Edit");

                entity.Property(e => e.IsView).HasColumnName("Is_View");

                entity.Property(e => e.MenuId).HasColumnName("Menu_Id");

                entity.Property(e => e.RoleId).HasColumnName("Role_Id");
            });

            modelBuilder.Entity<RltRoutePlan>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_ROUTE_PLAN");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CarClassPkid).HasColumnName("Car_Class_PKID");

                entity.Property(e => e.CarPkid).HasColumnName("Car_PKID");

                entity.Property(e => e.ChargesMasterPkid).HasColumnName("Charges_Master_PKID");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.FecilitiesPkid).HasColumnName("Fecilities_PKID");

                entity.Property(e => e.FromCityPkid).HasColumnName("From_City_PKID");

                entity.Property(e => e.IsActive)
                    .HasColumnName("Is_Active")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.ToCityPkid).HasColumnName("To_City_PKID");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltRoutesDetails>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_ROUTES_DETAILS");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CarCategoryPkid).HasColumnName("Car_Category_PKID");

                entity.Property(e => e.CarTripTypePkid).HasColumnName("Car_Trip_Type_PKID");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.DropoffCityPkid).HasColumnName("Dropoff_City_PKID");

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.PickupCityPkid).HasColumnName("Pickup_City_PKID");

                entity.Property(e => e.PkGuid)
                    .HasColumnName("PK_GUID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.RouteDistanceKm).HasColumnName("Route_Distance_KM");

                entity.Property(e => e.TotalPaidTollsInRoute).HasColumnName("Total_Paid_Tolls_In_Route");

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");

                entity.HasOne(d => d.CarCategoryPk)
                    .WithMany(p => p.RltRoutesDetails)
                    .HasForeignKey(d => d.CarCategoryPkid)
                    .HasConstraintName("FK_RLT_ROUTES_DETAILS_RLT_CAR_CATEGORY");

                entity.HasOne(d => d.CarTripTypePk)
                    .WithMany(p => p.RltRoutesDetails)
                    .HasForeignKey(d => d.CarTripTypePkid)
                    .HasConstraintName("FK_RLT_ROUTES_DETAILS_RLT_TRIP_TYPES");

                entity.HasOne(d => d.DropoffCityPk)
                    .WithMany(p => p.RltRoutesDetailsDropoffCityPk)
                    .HasForeignKey(d => d.DropoffCityPkid)
                    .HasConstraintName("FK_RLT_ROUTES_DETAILS_RLT_CITY_DROPOFF");

                entity.HasOne(d => d.PickupCityPk)
                    .WithMany(p => p.RltRoutesDetailsPickupCityPk)
                    .HasForeignKey(d => d.PickupCityPkid)
                    .HasConstraintName("FK_RLT_ROUTES_DETAILS_RLT_CITY_PICKUP");
            });

            modelBuilder.Entity<RltServices>(entity =>
            {
                entity.HasKey(e => e.Pkid)
                    .HasName("PK_tblService");

                entity.ToTable("RLT_Services");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.BriefIntroAboutService).HasMaxLength(200);

                entity.Property(e => e.IsActive).HasDefaultValueSql("((0))");

                entity.Property(e => e.ServiceIconClass).HasMaxLength(50);

                entity.Property(e => e.ServiceImagePath).HasMaxLength(50);

                entity.Property(e => e.ServiceName).HasMaxLength(50);
            });

            modelBuilder.Entity<RltState>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_STATE");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CounrtyPkid).HasColumnName("Counrty_PKID");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.IsActive)
                    .HasColumnName("Is_Active")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.PkGuid)
                    .HasColumnName("PK_GUID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.StateAbbr)
                    .HasColumnName("State_Abbr")
                    .HasMaxLength(5)
                    .IsUnicode(false);

                entity.Property(e => e.StateName)
                    .HasColumnName("State_Name")
                    .HasMaxLength(40)
                    .IsUnicode(false);

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");

                entity.HasOne(d => d.CounrtyPk)
                    .WithMany(p => p.RltState)
                    .HasForeignKey(d => d.CounrtyPkid)
                    .HasConstraintName("FK_RLT_STATE_RLT_COUNTRY");
            });

            modelBuilder.Entity<RltSubscribers>(entity =>
            {
                entity.ToTable("RLT_SUBSCRIBERS");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.IsValid).HasDefaultValueSql("((0))");

                entity.Property(e => e.SubsciberMailId)
                    .HasColumnName("SubsciberMailID")
                    .HasMaxLength(50);

                entity.Property(e => e.SubscriptionDate)
                    .HasColumnType("date")
                    .HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<RltTemplateMailMessage>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_Template_Mail_Message");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.MailId).HasMaxLength(50);

                entity.Property(e => e.MailSmsType)
                    .HasColumnName("Mail_SMS_Type")
                    .HasMaxLength(10);

                entity.Property(e => e.MessageBody)
                    .HasColumnName("Message_Body")
                    .HasColumnType("text");

                entity.Property(e => e.MobileNo).HasMaxLength(50);

                entity.Property(e => e.Subject).HasMaxLength(500);
            });

            modelBuilder.Entity<RltTemplateMaster>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_Template_Master");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.MailSmsType)
                    .HasColumnName("Mail_SMS_Type")
                    .HasMaxLength(50);

                entity.Property(e => e.MessageBody)
                    .HasColumnName("Message_Body")
                    .HasColumnType("text");

                entity.Property(e => e.Subject).HasMaxLength(500);

                entity.Property(e => e.TemplateName)
                    .HasColumnName("Template_Name")
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<RltTermsConditions>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_TermsConditions");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.TermsConditions).HasColumnType("text");
            });

            modelBuilder.Entity<RltTripTypes>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_TRIP_TYPES");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.TripType)
                    .HasColumnName("Trip_Type")
                    .HasMaxLength(50);

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<RltUserFeedBack>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_UserFeedBack");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.Country).HasMaxLength(50);

                entity.Property(e => e.CreateDate)
                    .HasColumnType("date")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.EmailId)
                    .HasColumnName("EmailID")
                    .HasMaxLength(50);

                entity.Property(e => e.IsActive).HasDefaultValueSql("((0))");

                entity.Property(e => e.Name).HasMaxLength(50);

                entity.Property(e => e.PhoneNo).HasMaxLength(50);

                entity.Property(e => e.Photo).HasMaxLength(150);
            });

            modelBuilder.Entity<RltUserLoginDetails>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_USER_LOGIN_DETAILS");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.LastLoginDateTime)
                    .HasColumnName("Last_Login_Date_Time")
                    .HasColumnType("datetime");

                entity.Property(e => e.Otpnumber).HasColumnName("OTPNumber");

                entity.Property(e => e.OtpupdatedDateTime)
                    .HasColumnName("OTPUpdatedDateTime")
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.PkGuid)
                    .HasColumnName("PK_GUID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.RltUser2faAuthStatus)
                    .HasColumnName("RLT_User_2FA_Auth_Status")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.RltUserAggreed)
                    .HasColumnName("RLT_User_Aggreed")
                    .HasMaxLength(10)
                    .IsFixedLength();

                entity.Property(e => e.RltUserDob)
                    .HasColumnName("RLT_User_DOB")
                    .HasColumnType("datetime");

                entity.Property(e => e.RltUserEmail)
                    .HasColumnName("RLT_User_Email")
                    .HasMaxLength(50);

                entity.Property(e => e.RltUserGender)
                    .HasColumnName("RLT_User_Gender")
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.RltUserLoginSignupMethod)
                    .HasColumnName("RLT_User_Login_Signup_Method")
                    .HasMaxLength(20);

                entity.Property(e => e.RltUserName)
                    .HasColumnName("RLT_User_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.RltUserPhone)
                    .HasColumnName("RLT_User_Phone")
                    .HasMaxLength(15);

                entity.Property(e => e.RltUserPwd)
                    .HasColumnName("RLT_User_Pwd")
                    .HasMaxLength(20);

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.UserId)
                    .HasColumnName("UserID")
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<RltVendorBankDetails>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_Vendor_Bank_Details");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.AccountHolderName)
                    .HasColumnName("Account_Holder_Name")
                    .HasMaxLength(60);

                entity.Property(e => e.AccountNumber)
                    .HasColumnName("Account_Number")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.BankNamePkid).HasColumnName("Bank_Name_PKID");

                entity.Property(e => e.BankStatus).HasColumnName("Bank_Status");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.IfscCode)
                    .HasColumnName("IFSC_CODE")
                    .HasMaxLength(15);

                entity.Property(e => e.IsActive).HasColumnName("Is_active");

                entity.Property(e => e.LastModifiedDate)
                    .HasColumnName("LastModified_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.LastmodifiedBy).HasColumnName("Lastmodified_By");

                entity.Property(e => e.VendorRefPkid).HasColumnName("Vendor_Ref_PKID");
            });

            modelBuilder.Entity<RltVendorCarDetails>(entity =>
            {
                entity.HasKey(e => e.Pkid)
                    .HasName("PK_RLT_VENDOR_CAR_DETAILS_1");

                entity.ToTable("RLT_VENDOR_CAR_DETAILS");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CarCompanyRef).HasColumnName("Car_Company_Ref");

                entity.Property(e => e.CarFuelTypeRef).HasColumnName("Car_Fuel_Type_Ref");

                entity.Property(e => e.CarNumber)
                    .HasColumnName("Car_Number")
                    .HasMaxLength(50);

                entity.Property(e => e.CarRegistrationDate)
                    .HasColumnName("Car_Registration_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.CarSegmentRef).HasColumnName("Car_Segment_Ref");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.DriverName)
                    .HasColumnName("Driver_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.DriverPhone1)
                    .HasColumnName("Driver_Phone1")
                    .HasMaxLength(15);

                entity.Property(e => e.DriverPhone2)
                    .HasColumnName("Driver_Phone2")
                    .HasMaxLength(15);

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.LastModifiedBy).HasColumnName("Last_Modified_By");

                entity.Property(e => e.LastModifiedDate)
                    .HasColumnName("Last_Modified_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.VendorRefId).HasColumnName("Vendor_Ref_ID");

                entity.HasOne(d => d.CarCompanyRefNavigation)
                    .WithMany(p => p.RltVendorCarDetails)
                    .HasForeignKey(d => d.CarCompanyRef)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_COMPANY");

                entity.HasOne(d => d.CarFuelTypeRefNavigation)
                    .WithMany(p => p.RltVendorCarDetails)
                    .HasForeignKey(d => d.CarFuelTypeRef)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_FUEL_TYPES");

                entity.HasOne(d => d.CarSegmentRefNavigation)
                    .WithMany(p => p.RltVendorCarDetails)
                    .HasForeignKey(d => d.CarSegmentRef)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_RLT_VENDOR_CAR_DETAILS_RLT_CAR_CATEGORY");

                entity.HasOne(d => d.VendorRef)
                    .WithMany(p => p.RltVendorCarDetails)
                    .HasForeignKey(d => d.VendorRefId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_RLT_VENDOR_CAR_DETAILS_RLT_Vendor_Details");
            });

            modelBuilder.Entity<RltVendorDetails>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_Vendor_Details");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Is2faActivated)
                    .HasColumnName("Is_2FA_Activated")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.IsActive)
                    .HasColumnName("Is_Active")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.LastModifiedBy)
                    .HasColumnName("Last_Modified_By")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.Phone1IsWhatsup)
                    .HasColumnName("Phone1_IsWhatsup")
                    .HasMaxLength(10);

                entity.Property(e => e.UpdatedDate)
                    .HasColumnName("Updated_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.VendorAddress)
                    .HasColumnName("Vendor_Address")
                    .HasMaxLength(200);

                entity.Property(e => e.VendorCompanyName)
                    .HasColumnName("Vendor_Company_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.VendorEmailId)
                    .HasColumnName("Vendor_EmailID")
                    .HasMaxLength(30);

                entity.Property(e => e.VendorLoginId)
                    .HasColumnName("Vendor_LoginID")
                    .HasMaxLength(15);

                entity.Property(e => e.VendorMemberId)
                    .HasColumnName("Vendor_Member_Id")
                    .HasMaxLength(15);

                entity.Property(e => e.VendorOwnerName)
                    .HasColumnName("Vendor_Owner_Name")
                    .HasMaxLength(50);

                entity.Property(e => e.VendorPhone1)
                    .HasColumnName("Vendor_Phone1")
                    .HasMaxLength(15);

                entity.Property(e => e.VendorPhone2)
                    .HasColumnName("Vendor_Phone2")
                    .HasMaxLength(15);

                entity.Property(e => e.VendorPhoto)
                    .HasColumnName("Vendor_Photo")
                    .HasMaxLength(500);

                entity.Property(e => e.VendorPwd)
                    .HasColumnName("Vendor_Pwd")
                    .HasMaxLength(15);
            });

            modelBuilder.Entity<RltVendorDocs>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("RLT_Vendor_Docs");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.Comments).HasMaxLength(250);

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.DocumentPath)
                    .HasColumnName("Document_Path")
                    .HasMaxLength(150);

                entity.Property(e => e.IsVerified).HasColumnName("Is_Verified");

                entity.Property(e => e.LastModifiedBy).HasColumnName("Last_Modified_By");

                entity.Property(e => e.LastModifiedDate)
                    .HasColumnName("Last_Modified_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.VendorDocId).HasColumnName("Vendor_Doc_Id");

                entity.Property(e => e.VendorRefPkid).HasColumnName("Vendor_Ref_PKID");

                entity.HasOne(d => d.VendorDoc)
                    .WithMany(p => p.RltVendorDocs)
                    .HasForeignKey(d => d.VendorDocId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_RLT_Vendor_Docs_RLT_DOCUMNETS_NAME");

                entity.HasOne(d => d.VendorRefPk)
                    .WithMany(p => p.RltVendorDocs)
                    .HasForeignKey(d => d.VendorRefPkid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_RLT_Vendor_Docs_RLT_Vendor_Details");
            });

            modelBuilder.Entity<Role>(entity =>
            {
                entity.ToTable("Role", "Identity");

                entity.HasIndex(e => e.NormalizedName)
                    .HasName("RoleNameIndex")
                    .IsUnique()
                    .HasFilter("([NormalizedName] IS NOT NULL)");

                entity.Property(e => e.Name).HasMaxLength(256);

                entity.Property(e => e.NormalizedName).HasMaxLength(256);
            });

            modelBuilder.Entity<RoleClaims>(entity =>
            {
                entity.ToTable("RoleClaims", "Identity");

                entity.HasIndex(e => e.RoleId);

                entity.Property(e => e.RoleId).IsRequired();

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.RoleClaims)
                    .HasForeignKey(d => d.RoleId);
            });

            modelBuilder.Entity<TempBooking>(entity =>
            {
                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.BookingDate)
                    .HasColumnName("Booking_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.DropOffAddress)
                    .HasColumnName("DropOff_Address")
                    .HasMaxLength(150);

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.MobileNumber)
                    .HasColumnName("Mobile_Number")
                    .HasMaxLength(20);

                entity.Property(e => e.PickUpAddress)
                    .HasColumnName("PickUp_Address")
                    .HasMaxLength(150);

                entity.Property(e => e.TravelDate)
                    .HasColumnName("Travel_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.TravelTime)
                    .HasColumnName("Travel_Time")
                    .HasMaxLength(10);

                entity.Property(e => e.UserName)
                    .HasColumnName("User_Name")
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<User>(entity =>
            {
                entity.ToTable("User", "Identity");

                entity.HasIndex(e => e.NormalizedEmail)
                    .HasName("EmailIndex");

                entity.HasIndex(e => e.NormalizedUserName)
                    .HasName("UserNameIndex")
                    .IsUnique()
                    .HasFilter("([NormalizedUserName] IS NOT NULL)");

                entity.Property(e => e.CurrentOtpnumber).HasColumnName("CurrentOTPNumber");

                entity.Property(e => e.Email).HasMaxLength(256);

                entity.Property(e => e.NormalizedEmail).HasMaxLength(256);

                entity.Property(e => e.NormalizedUserName).HasMaxLength(256);

                entity.Property(e => e.OtpexpireTimeInMinute).HasColumnName("OTPExpireTimeInMinute");

                entity.Property(e => e.OtpgeneratedDate).HasColumnName("OTPGeneratedDate");

                entity.Property(e => e.UserName).HasMaxLength(256);
            });

            modelBuilder.Entity<UserClaims>(entity =>
            {
                entity.ToTable("UserClaims", "Identity");

                entity.HasIndex(e => e.UserId);

                entity.Property(e => e.UserId).IsRequired();

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserClaims)
                    .HasForeignKey(d => d.UserId);
            });

            modelBuilder.Entity<UserLogins>(entity =>
            {
                entity.HasKey(e => new { e.LoginProvider, e.ProviderKey });

                entity.ToTable("UserLogins", "Identity");

                entity.HasIndex(e => e.UserId);

                entity.Property(e => e.UserId).IsRequired();

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserLogins)
                    .HasForeignKey(d => d.UserId);
            });

            modelBuilder.Entity<UserRoles>(entity =>
            {
                entity.HasKey(e => new { e.UserId, e.RoleId });

                entity.ToTable("UserRoles", "Identity");

                entity.HasIndex(e => e.RoleId);

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.UserRoles)
                    .HasForeignKey(d => d.RoleId);

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserRoles)
                    .HasForeignKey(d => d.UserId);
            });

            modelBuilder.Entity<UserTokens>(entity =>
            {
                entity.HasKey(e => new { e.UserId, e.LoginProvider, e.Name });

                entity.ToTable("UserTokens", "Identity");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserTokens)
                    .HasForeignKey(d => d.UserId);
            });

            modelBuilder.Entity<VendorCarDriverDocs>(entity =>
            {
                entity.HasKey(e => e.Pkid);

                entity.ToTable("Vendor_Car_Driver_Docs");

                entity.Property(e => e.Pkid).HasColumnName("PKID");

                entity.Property(e => e.Comments).HasMaxLength(250);

                entity.Property(e => e.CreatedDate)
                    .HasColumnName("Created_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.DocName).HasColumnName("Doc_Name");

                entity.Property(e => e.DocumentPath)
                    .HasColumnName("Document_Path")
                    .HasMaxLength(150);

                entity.Property(e => e.IsVerified).HasColumnName("Is_Verified");

                entity.Property(e => e.LastModifiedBy).HasColumnName("Last_Modified_By");

                entity.Property(e => e.LastModifiedDate)
                    .HasColumnName("Last_Modified_Date")
                    .HasColumnType("datetime");

                entity.Property(e => e.VendorCarRef).HasColumnName("Vendor_Car_Ref");

                entity.HasOne(d => d.DocNameNavigation)
                    .WithMany(p => p.VendorCarDriverDocs)
                    .HasForeignKey(d => d.DocName)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Vendor_Car_Driver_Docs_RLT_DOCUMNETS_NAME");

                entity.HasOne(d => d.VendorCarRefNavigation)
                    .WithMany(p => p.VendorCarDriverDocs)
                    .HasForeignKey(d => d.VendorCarRef)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Vendor_Car_Driver_Docs_RLT_VENDOR_CAR_DETAILS");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
