﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltPaymentMethod
    {
        public RltPaymentMethod()
        {
            RltBookingPaymentDetails = new HashSet<RltBookingPaymentDetails>();
        }

        public int Pkid { get; set; }
        public string PaymentMethodName { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }

        public virtual ICollection<RltBookingPaymentDetails> RltBookingPaymentDetails { get; set; }
    }
}
