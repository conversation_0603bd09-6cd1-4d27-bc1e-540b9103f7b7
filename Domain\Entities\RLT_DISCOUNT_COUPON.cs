﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Domain.Entities
{
   public class RLT_DISCOUNT_COUPON : AuditableProperty
    {
        [Key]
        public int PKID { get; set; }
        public string Discount_Coupon { get; set; }
        public decimal Discount { get; set; }
        public string Coupon_Name { get; set; }
        public string Coupon_Remark { get; set; }
        public int Max_Discount { get; set; }
        public int Fare_When_Applied { get; set; }
        public DateTime Coupon_Last_Date { get; set; }
    
    }
}
