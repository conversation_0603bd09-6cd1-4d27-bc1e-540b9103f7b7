﻿using Application.DTOs.Booking;
using System;
using System.Collections.Generic;
using System.Text;

namespace Application.Features.Invoicing.ViewModel
{
    public class GetAllInvoiceViewModel
    {
        public string PickUpCity { get; set; }
        public string DropOffCity { get; set; }

        public string PickUpLongLat { get; set; }
        public string DropOffLongLat { get; set; }

        public string PickUpAddress { get; set; }
        public string DropOffAddress { get; set; }

        public string PickUpDate { get; set; }
        public string PickUpTime { get; set; }

        public string DropOffDate { get; set; }
        public string DropOffTime { get; set; }

        public string DriverName { get; set; }
        public string DriverPhone { get; set; }
        public string CabNumber { get; set; }

        public string DriverPhoto { get; set; }
        public FareDetails TripFareDetail {get;set;}

    }
}
