﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltTripTypes
    {
        public RltTripTypes()
        {
            RltBookingPaymentDetails = new HashSet<RltBookingPaymentDetails>();
            RltRoutesDetails = new HashSet<RltRoutesDetails>();
        }

        public int Pkid { get; set; }
        public string TripType { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }

        public virtual ICollection<RltBookingPaymentDetails> RltBookingPaymentDetails { get; set; }
        public virtual ICollection<RltRoutesDetails> RltRoutesDetails { get; set; }
    }
}
