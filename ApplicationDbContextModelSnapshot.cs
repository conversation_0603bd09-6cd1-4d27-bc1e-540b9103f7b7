﻿// <auto-generated />
using System;
using Infrastructure.Persistence.Contexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Infrastructure.Persistence.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "3.1.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

            modelBuilder.Entity("Domain.Entities.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Barcode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Rate")
                        .HasColumnType("decimal(18,6)");

                    b.HasKey("Id");

                    b.ToTable("Products");
                });

            //modelBuilder.Entity("Domain.Entities.RLT_BOOKING", b =>
            //{
            //    b.Property<int>("PKID")
            //        .ValueGeneratedOnAdd()
            //        .HasColumnType("bigint")
            //        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

            //    b.Property<string>("Booking_Id")
            //        .HasColumnType("nvarchar(40)");

            //    b.Property<int>("City_From_Id")
            //        .HasColumnType("int");

            //    b.Property<int>("City_To_Id")
            //        .HasColumnType("int");

            //    b.Property<int>("Trip_Type_Id")
            //        .HasColumnType("int");

            //    b.Property<int>("Car_Category_Id")
            //        .HasColumnType("int");

            //    b.Property<string>("Duration")
            //        .HasColumnType("nvarchar(100)");

            //    b.Property<decimal>("Distance")
            //        .HasColumnType("decimal(18,2)");

            //    b.Property<decimal>("Basic_Fare")
            //        .HasColumnType("decimal(18,2)");


            //    b.Property<decimal>("Driver_Charge")
            //       .HasColumnType("decimal(18,2)");

            //    b.Property<decimal>("Toll_Charge")
            //        .HasColumnType("decimal(18,2)");

            //    b.Property<DateTime?>("GST")
            //        .HasColumnType("decimal(18,2)");

            //    b.Property<decimal>("Fare")
            //        .HasColumnType("decimal(18,2)");

            //    b.Property<decimal>("GST_Fare")
            //        .HasColumnType("decimal(18,2)");

            //    b.Property<string>("Coupon_Code")
            //        .HasColumnType("nvarchar(100)");

            //    b.Property<decimal>("Coupon_Discount")
            //       .HasColumnType("decimal(18,2)");

            //    b.Property<DateTime>("Booking_Date")
            //        .HasColumnType("date");

            //    b.Property<string>("PickUp_Address")
            //        .HasColumnType("nvarchar(400)");

            //    b.Property<string>("DropOff_Address")
            //        .HasColumnType("nvarchar(400)");

            //    b.Property<DateTime>("PickUp_Date")
            //        .HasColumnType("date");

            //    b.Property<string>("PickUp_Time")
            //        .HasColumnType("nvarchar(100)");



            //    b.Property<string>("Name")
            //      .HasColumnType("nvarchar(100)");

            //    b.Property<string>("Mobile_No1")
            //        .HasColumnType("nvarchar(100)");

            //    b.Property<string?>("Mobile_No2")
            //        .HasColumnType("nvarchar(100)");

            //    b.Property<string>("Mail_Id")
            //        .HasColumnType("nvarchar(100)");

            //    b.Property<int>("Mode_Of_Payment_Id")
            //        .HasColumnType("int");

            //    b.Property<int>("Vendor_Id")
            //        .HasColumnType("int");

            //    b.Property<int>("Car_Id")
            //      .HasColumnType("int");

            //    b.Property<int>("Booking_Status_Id")
            //        .HasColumnType("int");

            //    b.Property<string>("Booking_Remark")
            //        .HasColumnType("nvarchar(1000)");

            //    b.Property<string>("Invoice_No")
            //        .HasColumnType("nvarchar(40)");

            //    b.Property<DateTime?>("Invoice_Date")
            //        .HasColumnType("date");

            //    b.Property<bool>("Is_Active")
            //        .HasColumnType("bit");

            //    b.Property<DateTime>("Created_Date")
            //       .HasColumnType("nvarchar(max)");

            //    b.Property<DateTime?>("Updated_Date")
            //        .HasColumnType("datetime2");

            //    b.Property<int>("Created_By")
            //        .HasColumnType("int");

            //    b.Property<int>("Updated_By")
            //        .HasColumnType("int");

            //    b.Property<decimal>("razorpay_payment_id")
            //        .HasColumnType("nvarchar(100)");


            //    b.Property<string>("razorpay_order_id")
            //      .HasColumnType("nvarchar(100)");

            //    b.Property<string?>("razorpay_signature")
            //        .HasColumnType("nvarchar(1000)");

            //    b.Property<string>("razorpay_status")
            //        .HasColumnType("nvarchar(100)");

            //    b.Property<string>("PickUpAddressLatitude")
            //        .HasColumnType("nvarchar(200)");

            //    b.Property<string>("PickUpAddressLongitude")
            //        .HasColumnType("nvarchar(200)");

            //    b.Property<string?>("BookingEditRemark")
            //      .HasColumnType("nvarchar(600)");

            //    b.Property<int>("driver_id")
            //        .HasColumnType("int");

            //    b.Property<string>("completepickupaddress")
            //        .HasColumnType("nvarchar(800)");

            //    b.Property<string>("completedropoffpaddress")
            //        .HasColumnType("nvarchar(800)");

            //    b.HasKey("PKId");

            //    b.ToTable("RLT_BOOKING");
            //});

#pragma warning restore 612, 618
        }
    }
}
