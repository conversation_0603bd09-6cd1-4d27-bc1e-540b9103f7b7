﻿using Application.DTOs.BasicCommon;
using Application.Interfaces.Repositories;
using Dapper;
using Infrastructure.Persistence.Contexts;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure.Persistence.Repositories
{
    public class CommonRepositoryAsync : ICommonRepositoryAsync
    {
        private readonly IConfiguration configuration;
        private readonly ApplicationDbContext _dbContext;
        public CommonRepositoryAsync(IConfiguration configuration, ApplicationDbContext dbContext)
        {

            this.configuration = configuration;
            _dbContext = dbContext;
        }

        public async Task<List<CityResponse>> GetCityByCityNames(string pickCityName, string dropOffCityName)
        {
            List<CityResponse> cityResponses = new List<CityResponse>();
            var _params = new
            {
                pickUpCityName = pickCityName,
                dropOffCityName = dropOffCityName
            };

            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();
                var result = await connection.QueryAsync<CityResponse>("usp_CityDetails_Get", _params, commandType: CommandType.StoredProcedure);

                foreach (var item in result)
                {
                    var city = new CityResponse()
                    {
                        CityName = item.CityName,
                        Longitude=item.Longitude ,
                        Latitude= item.Latitude,
                        Eloc= item.Eloc
                      };

                    cityResponses.Add(item);
                }
                return cityResponses;
            }
        }

        public async Task<List<MostFavouriteRoutesResponse>> GetMostFavouriteRoutes()
        {
            List<MostFavouriteRoutesResponse> mostFavouriteRoutesResponses = new List<MostFavouriteRoutesResponse>();

            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();
                var result = await connection.QueryAsync<MostFavouriteRoutesResponse>("usp_GetMostFavoriteRoutesDetails_GET", commandType: CommandType.StoredProcedure);
                foreach (var item in result)
                {
                    var city = new MostFavouriteRoutesResponse()
                    {
                        RouteName= item.PickUpCity+" To "+ item.DropOffCity,
                        RouteLink = item.PickUpCity + "-To-" + item.DropOffCity,
                        PickUpCity =item.PickUpCity,
                        DropOffCity = item.DropOffCity,
                        CityImage = item.CityImage,
                        CategoryName = item.CategoryName,
                        CategoryAbbr = item.CategoryAbbr,
                        Features = item.Features,
                        TripType = item.TripType,
                        Fare = item.Fare
                    };

                    mostFavouriteRoutesResponses.Add(item);
                }

                return mostFavouriteRoutesResponses;
            }
        }

        public async Task<DiscountCouponResponse> VerifyDiscountCoupon(string couponCode)
        {
            var _params = new
            {
                couponCode = couponCode
            };

            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();
                var result = await connection.QueryFirstOrDefaultAsync<DiscountCouponResponse>("usp_DiscountCoupon_Verify", _params, commandType: CommandType.StoredProcedure);
                return result;
            }
        }

    
    }
}
