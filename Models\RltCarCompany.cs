﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltCarCompany
    {
        public RltCarCompany()
        {
            RltCarModel = new HashSet<RltCarModel>();
            RltVendorCarDetails = new HashSet<RltVendorCarDetails>();
        }

        public int Pkid { get; set; }
        public Guid? PkGuid { get; set; }
        public string CompanyName { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }

        public virtual ICollection<RltCarModel> RltCarModel { get; set; }
        public virtual ICollection<RltVendorCarDetails> RltVendorCarDetails { get; set; }
    }
}
