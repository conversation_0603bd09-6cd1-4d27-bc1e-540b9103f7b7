﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace WebApi.Controllers.v1
{
    public class GoogleMapsController : BaseApiController
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;

        public GoogleMapsController(IConfiguration configuration, HttpClient httpClient)
        {
            _configuration = configuration;
            _httpClient = httpClient;
        }

        [HttpGet("places/autocomplete")]
        public async Task<IActionResult> GetPlacesAutocomplete([FromQuery] string input)
        {
            var apiKey = _configuration["GoogleMaps:ApiKey"];
            var url = $"https://maps.googleapis.com/maps/api/place/autocomplete/json?input={input}&components=country:IN&key={apiKey}";

            var response = await _httpClient.GetStringAsync(url);
            var googleResponse = JsonConvert.DeserializeObject<GooglePlacesAutocompleteResponse>(response);

            var result = new
            {
                suggestedLocations = googleResponse.predictions.Select(p => new
                {
                    placeName = p.structured_formatting.main_text,
                    placeAddress = p.description,
                    latitude = (string)null,
                    longitude = (string)null,
                    placeId = p.place_id
                })
            };

            return Ok(result);
        }

        [HttpGet("directions")]
        public async Task<IActionResult> GetDirections([FromQuery] string fromLatLong, [FromQuery] string toLatLong)
        {
            var apiKey = _configuration["GoogleMaps:ApiKey"];
            var url = $"https://maps.googleapis.com/maps/api/directions/json?origin={fromLatLong}&destination={toLatLong}&key={apiKey}";

            var response = await _httpClient.GetStringAsync(url);
            var googleResponse = JsonConvert.DeserializeObject<GoogleDirectionsResponse>(response);

            if (googleResponse.routes?.Length > 0)
            {
                var route = googleResponse.routes[0];
                var leg = route.legs[0];

                var result = new
                {
                    routes = new[]
                    {
                    new
                    {
                        distance = leg.distance.value,
                        duration = leg.duration.value,
                        geometry = route.overview_polyline.points,
                        legs = new[]
                        {
                            new
                            {
                                distance = leg.distance.value,
                                duration = leg.duration.value,
                                start_location = new
                                {
                                    lat = leg.start_location.lat,
                                    lng = leg.start_location.lng
                                },
                                end_location = new
                                {
                                    lat = leg.end_location.lat,
                                    lng = leg.end_location.lng
                                }
                            }
                        }
                    }
                }
                };

                return Ok(result);
            }

            return BadRequest("No routes found");
        }

        [HttpGet("geocode/reverse")]
        public async Task<IActionResult> ReverseGeocode([FromQuery] double lat, [FromQuery] double lng)
        {
            var apiKey = _configuration["GoogleMaps:ApiKey"];
            var url = $"https://maps.googleapis.com/maps/api/geocode/json?latlng={lat},{lng}&key={apiKey}";

            var response = await _httpClient.GetStringAsync(url);
            var googleResponse = JsonConvert.DeserializeObject<GoogleGeocodingResponse>(response);

            if (googleResponse.results?.Length > 0)
            {
                var result = googleResponse.results[0];
                var city = "";
                var state = "";
                var country = "";

                foreach (var component in result.address_components)
                {
                    if (component.types.Contains("locality"))
                        city = component.long_name;
                    else if (component.types.Contains("administrative_area_level_1"))
                        state = component.long_name;
                    else if (component.types.Contains("country"))
                        country = component.long_name;
                }

                return Ok(new
                {
                    city,
                    state,
                    country,
                    formatted_address = result.formatted_address,
                    results = googleResponse.results
                });
            }

            return BadRequest("No results found");
        }

        [HttpGet("places/textsearch")]
        public async Task<IActionResult> TextSearch([FromQuery] string query)
        {
            var apiKey = _configuration["GoogleMaps:ApiKey"];
            var url = $"https://maps.googleapis.com/maps/api/place/textsearch/json?query={query}&key={apiKey}";

            var response = await _httpClient.GetStringAsync(url);
            var googleResponse = JsonConvert.DeserializeObject<GooglePlacesTextSearchResponse>(response);

            var result = new
            {
                suggestedLocations = googleResponse.results.Select(place => new
                {
                    placeName = place.name,
                    placeAddress = place.formatted_address,
                    latitude = place.geometry.location.lat,
                    longitude = place.geometry.location.lng,
                    placeId = place.place_id
                })
            };

            return Ok(result);
        }
    }

    public class GooglePlacesAutocompleteResponse
    {
        public Prediction[] predictions { get; set; }
    }

    public class Prediction
    {
        public string description { get; set; }
        public string place_id { get; set; }
        public StructuredFormatting structured_formatting { get; set; }
    }

    public class StructuredFormatting
    {
        public string main_text { get; set; }
        public string secondary_text { get; set; }
    }

    public class GoogleDirectionsResponse
    {
        public Route[] routes { get; set; }
    }

    public class Route
    {
        public Leg[] legs { get; set; }
        public OverviewPolyline overview_polyline { get; set; }
    }

    public class Leg
    {
        public Distance distance { get; set; }
        public Duration duration { get; set; }
        public Location start_location { get; set; }
        public Location end_location { get; set; }
    }

    public class Distance
    {
        public string text { get; set; }
        public int value { get; set; }
    }

    public class Duration
    {
        public string text { get; set; }
        public int value { get; set; }
    }

    public class Location
    {
        public double lat { get; set; }
        public double lng { get; set; }
    }

    public class OverviewPolyline
    {
        public string points { get; set; }
    }

    public class GoogleGeocodingResponse
    {
        public GeocodingResult[] results { get; set; }
    }

    public class GeocodingResult
    {
        public AddressComponent[] address_components { get; set; }
        public string formatted_address { get; set; }
        public Geometry geometry { get; set; }
    }

    public class AddressComponent
    {
        public string long_name { get; set; }
        public string short_name { get; set; }
        public string[] types { get; set; }
    }

    public class Geometry
    {
        public Location location { get; set; }
    }

    public class GooglePlacesTextSearchResponse
    {
        public GooglePlaceResult[] results { get; set; }
    }

    public class GooglePlaceResult
    {
        public string name { get; set; }
        public string formatted_address { get; set; }
        public Geometry geometry { get; set; }
        public string place_id { get; set; }
    }
}
