﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltVendorDocs
    {
        public int Pkid { get; set; }
        public int VendorDocId { get; set; }
        public int VendorRefPkid { get; set; }
        public string DocumentPath { get; set; }
        public bool IsVerified { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public int LastModifiedBy { get; set; }
        public string Comments { get; set; }

        public virtual RltDocumnetsName VendorDoc { get; set; }
        public virtual RltVendorDetails VendorRefPk { get; set; }
    }
}
