﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltBookingPaymentDetails
    {
        public int Pkid { get; set; }
        public string PassengerName { get; set; }
        public string PassengerEmail { get; set; }
        public string PassengerPhone { get; set; }
        public string PassengerAltPhone { get; set; }
        public string PassengerSpecialRequestComment { get; set; }
        public string PassengerTripPurpose { get; set; }
        public int? PassengerTripTypePkid { get; set; }
        public int? PassengerPickupCityPkid { get; set; }
        public string PassengerPickupAddress { get; set; }
        public int? PassengerDropoffCityPkid { get; set; }
        public string PassengerDropoffAddress { get; set; }
        public long? BookingLoginPkid { get; set; }
        public double? BaseFare { get; set; }
        public double? DriverAllowance { get; set; }
        public double? ServiceTax { get; set; }
        public double? Discount { get; set; }
        public string DiscountCoupon { get; set; }
        public double? TotalBookingAmount { get; set; }
        public int? PaymentModePkid { get; set; }
        public double? PaymentAmountRecieved { get; set; }
        public double? PaymentNeedToCollect { get; set; }
        public string PaymentIssueComment { get; set; }
        public string PaymentTransactionId { get; set; }
        public string PaymentTransactionStatus { get; set; }

        public virtual RltUserLoginDetails BookingLoginPk { get; set; }
        public virtual RltCity PassengerDropoffCityPk { get; set; }
        public virtual RltCity PassengerPickupCityPk { get; set; }
        public virtual RltTripTypes PassengerTripTypePk { get; set; }
        public virtual RltPaymentMethod PaymentModePk { get; set; }
    }
}
