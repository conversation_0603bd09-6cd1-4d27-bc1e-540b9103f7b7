﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Application.Enums
{
  public enum PaymentStatus
    {
        [Description("Pending")]
        Pending=1,
        [Description("Paid")]
        Paid =2,
        [Description("Failed")]
        Failed =3,
        [Description("Created")]
        Created =4,
        [Description("PartialPaid")]
        PartialPaid =5,
        [Description("CashPaidToDriver")]
        CashPaidToDriver =6,
        [Description("Attempted")]
        Attempted =7
    }

    public enum BookingStatus
    {
        [Description("Pending")]
        Pending =1,
        [Description("Confirmed")]
        Confirmed =2,
        [Description("CabAssigned")]
        CabAssigned =3,
        [Description("TripStated")]
        TripStated =4,
        [Description("NotConfirmd")]
        NotConfirmd =5,
        [Description("Cancelled")]
        Cancelled =6,
        [Description("Completed")]
        Completed =7,
        [Description("TripAborted")]
        TripAborted = 8
    }
}
