﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltState
    {
        public RltState()
        {
            RltCity = new HashSet<RltCity>();
        }

        public int Pkid { get; set; }
        public Guid? PkGuid { get; set; }
        public string StateName { get; set; }
        public string StateAbbr { get; set; }
        public int? CounrtyPkid { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }

        public virtual RltCountry CounrtyPk { get; set; }
        public virtual ICollection<RltCity> RltCity { get; set; }
    }
}
