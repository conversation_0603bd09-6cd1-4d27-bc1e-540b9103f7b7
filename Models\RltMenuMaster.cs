﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltMenuMaster
    {
        public int Pkid { get; set; }
        public string MenuName { get; set; }
        public string MenuIcon { get; set; }
        public string MenuUrl { get; set; }
        public string PageId { get; set; }
        public int ParentMenuId { get; set; }
        public int? IsAdminMenu { get; set; }
        public bool? IsActive { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string ActiveMenuClass { get; set; }
        public double? OrderNumber { get; set; }
    }
}
