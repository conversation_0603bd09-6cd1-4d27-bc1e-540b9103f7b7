﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltCarDocs
    {
        public int Pkid { get; set; }
        public int CarPkid { get; set; }
        public int CarDocId { get; set; }
        public DateTime? CarDocEndDate { get; set; }
        public string DocumentPath { get; set; }
        public bool IsVerified { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public int LastModifiedBy { get; set; }
    }
}
