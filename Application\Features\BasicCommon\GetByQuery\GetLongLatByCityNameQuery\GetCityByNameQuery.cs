﻿using Application.DTOs.BasicCommon;
using Application.Exceptions;
using Application.Interfaces.Repositories;
using Application.Wrappers;
using MediatR;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Application.Features.BasicCommon.GetByQuery.GetLongLatByCityNameQuery
{
   public class GetCityByNameQuery : IRequest<Response<List<CityResponse>>>
    {
        public string PickUpCityName { get; set; }
        public string DropOffCityName { get; set; }

        public class GetCityByNameQueryHandler : IRequestHandler<GetCityByNameQuery, Response<List<CityResponse>>>
        {
            private readonly ICommonRepositoryAsync _commonRepositoryAsync;
            public GetCityByNameQueryHandler(ICommonRepositoryAsync commonRepositoryAsync)
            {
                _commonRepositoryAsync = commonRepositoryAsync;
            }
            public async Task<Response<List<CityResponse>>> Handle(GetCityByNameQuery query, CancellationToken cancellationToken)
            {

                var city = await _commonRepositoryAsync.GetCityByCityNames(query.PickUpCityName,query.DropOffCityName);
                if (city == null) throw new ApiException($"Invalid PickUp and DropOff City.");
                return new Response<List<CityResponse>>(city);
            }
        }

    }
}
