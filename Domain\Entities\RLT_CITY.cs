﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Domain.Entities
{
  public class RLT_CITY
    {
        [Key]
        public int PKID { get; set; }
        public Guid PK_GUID { get; set; }
        public string City_Name { get; set; }
        public string City_Abbr { get; set; }
        public bool Is_Active { get; set; }
	    public DateTime Created_Date { get; set; }
        public DateTime Updated_Date { get; set; }
        public string State_PKID { get; set; }
        public string latitude { get; set; }
        public string longitude { get; set; }
        public string eLoc { get; set; }
        public int orderIndex { get; set; }
        public string score { get; set; }
    }
}
