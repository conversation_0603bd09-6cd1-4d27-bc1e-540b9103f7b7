﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltVendorDetails
    {
        public RltVendorDetails()
        {
            RltVendorCarDetails = new HashSet<RltVendorCarDetails>();
            RltVendorDocs = new HashSet<RltVendorDocs>();
        }

        public int Pkid { get; set; }
        public string VendorCompanyName { get; set; }
        public string VendorOwnerName { get; set; }
        public string VendorPhoto { get; set; }
        public string VendorEmailId { get; set; }
        public string VendorPhone1 { get; set; }
        public string VendorPhone2 { get; set; }
        public string Phone1IsWhatsup { get; set; }
        public string VendorAddress { get; set; }
        public string VendorMemberId { get; set; }
        public string VendorLoginId { get; set; }
        public string VendorPwd { get; set; }
        public bool? IsActive { get; set; }
        public bool? Is2faActivated { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public int? LastModifiedBy { get; set; }

        public virtual ICollection<RltVendorCarDetails> RltVendorCarDetails { get; set; }
        public virtual ICollection<RltVendorDocs> RltVendorDocs { get; set; }
    }
}
