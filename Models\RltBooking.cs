﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltBooking
    {
        public long Pkid { get; set; }
        public string BookingId { get; set; }
        public int? CityFromId { get; set; }
        public int? CityToId { get; set; }
        public int? TripTypeId { get; set; }
        public int? CarCategoryId { get; set; }
        public string Duration { get; set; }
        public decimal? Distance { get; set; }
        public decimal? BasicFare { get; set; }
        public decimal? DriverCharge { get; set; }
        public decimal? TollCharge { get; set; }
        public decimal? Gst { get; set; }
        public decimal? Fare { get; set; }
        public decimal? GstFare { get; set; }
        public string CouponCode { get; set; }
        public decimal? CouponDiscount { get; set; }
        public DateTime? BookingDate { get; set; }
        public string PickUpAddress { get; set; }
        public string DropOffAddress { get; set; }
        public DateTime? PickUpDate { get; set; }
        public string PickUpTime { get; set; }
        public string Name { get; set; }
        public string MobileNo1 { get; set; }
        public string MobileNo2 { get; set; }
        public string MailId { get; set; }
        public int? ModeOfPaymentId { get; set; }
        public int? VendorId { get; set; }
        public int? CarId { get; set; }
        public int? BookingStatusId { get; set; }
        public string BookingRemark { get; set; }
        public string InvoiceNo { get; set; }
        public DateTime? InvoiceDate { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public int? UpdatedBy { get; set; }
        public string RazorpayPaymentId { get; set; }
        public string RazorpayOrderId { get; set; }
        public string RazorpaySignature { get; set; }
        public string RazorpayStatus { get; set; }
        public string PickUpAddressLatitude { get; set; }
        public string PickUpAddressLongitude { get; set; }
        public string BookingEditRemark { get; set; }
        public int? DriverId { get; set; }
        public string Completepickupaddress { get; set; }
        public string Completedropoffpaddress { get; set; }
    }
}
