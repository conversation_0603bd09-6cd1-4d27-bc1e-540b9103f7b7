﻿using Application.Interfaces;
using Domain.Settings;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace Infrastructure.Shared.Services
{
    public class MapService : IMapService
    {
        public MapSettings _mapSettings { get; }
        public ILogger<MapService> _logger { get; }

        public MapService(IOptions<MapSettings> mapSettings, ILogger<MapService> logger)
        {
            _mapSettings = mapSettings.Value;
            _logger = logger;
        }

        public async Task<string> GenerateMapToken()
        {
            string oauthAPI = this._mapSettings.OAuthAPI + "grant_type=" + this._mapSettings.GrantType + "&client_id=" + this._mapSettings.ClientId + "&client_secret=" + this._mapSettings.ClientSecret;

            string obj;
            using (var httpClient = new HttpClient())
            {
                using (var response = await httpClient.PostAsync(oauthAPI, null))
                {
                    string apiResponse = await response.Content.ReadAsStringAsync();
                    obj = apiResponse;
                }
            }

            return await Task.Factory.StartNew(function: () => obj);
        }

        public async Task MapAutoSuggest(string placeName, string token, string eloc)
        {
            string obj;
            using (var httpClient = new HttpClient())
            {
                using (var response = await httpClient.GetAsync(this._mapSettings.AutoSuggestAPI + placeName + "&filter=cop:" + eloc + "&access_token=" + token))
                {
                    string apiResponse = await response.Content.ReadAsStringAsync();
                    obj = apiResponse;
                }
            }
        }

        public async Task MapCitySearch(string placeName, string token)
        {

            string obj;
            using (var httpClient = new HttpClient())
            {
                using (var response = await httpClient.GetAsync(this._mapSettings.GetCityAPI + placeName + "&access_token=" + token))
                {
                    string apiResponse = await response.Content.ReadAsStringAsync();
                    obj = apiResponse;
                }
            }
        }

        public async Task MapDistanceMatrix(string fromLatLang, string toLatLang)
        {
            string obj;
            using (var httpClient = new HttpClient())
            {
                using (var response = await httpClient.GetAsync(this._mapSettings.DistnaceMatrixAPI + fromLatLang + ";" + toLatLang + "?sources=0;1&destinations=2;3"))
                {
                    string apiResponse = await response.Content.ReadAsStringAsync();
                    obj = apiResponse;
                }
            }
        }

        public async Task MapEtaCalculation(string fromLatLong, string toLatLong)
        {
            string obj;
            using (var httpClient = new HttpClient())
            {
                using (var response = await httpClient.GetAsync(this._mapSettings.EtaAPI + fromLatLong + ";" + toLatLong + "?alternatives=true&&geometries=polyline&overview=full&exclude=&steps=true&region=ind"))
                {
                    string apiResponse = await response.Content.ReadAsStringAsync();
                    obj = apiResponse;
                }
            }
        }

        public async Task MapNearBy(string refLocation, string userName, string keyword, string token)
        {
            string obj;
            using (var httpClient = new HttpClient())
            {
                using (var response = await httpClient.GetAsync(this._mapSettings.NearByAPI + "&username =" + userName + "&refLocation=" + refLocation + "&keywords=" + keyword + "&access_token=" + token))
                {
                    string apiResponse = await response.Content.ReadAsStringAsync();
                    obj = apiResponse;
                }
            }
        }

        public async Task MapTextSearch(string placeName, string token)
        {
            string obj;
            using (var httpClient = new HttpClient())
            {
                using (var response = await httpClient.GetAsync(this._mapSettings.TextSearchAPI + placeName + "&access_token=" + token))
                {
                    string apiResponse = await response.Content.ReadAsStringAsync();
                    obj = apiResponse;
                }
            }
        }
    }
}
