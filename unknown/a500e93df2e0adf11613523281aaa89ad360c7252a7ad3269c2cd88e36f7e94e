-- =============================================
-- Schema Update: Add Booking_Created_By Column
-- Description: Adds username-based user identification to RLT_BOOKING table
-- Author: CabYaari Development Team
-- Created: 2025-07-06
-- =============================================

USE [CabYaari]
GO

-- Check if column already exists
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RLT_BOOKING]') AND name = 'Booking_Created_By')
BEGIN
    PRINT 'Adding Booking_Created_By column to RLT_BOOKING table...'
    
    -- Add the new column
    ALTER TABLE [dbo].[RLT_BOOKING]
    ADD Booking_Created_By NVARCHAR(50) NULL
    
    PRINT 'Booking_Created_By column added successfully.'
    
    -- Update existing records to populate the new column
    PRINT 'Updating existing records with username mapping...'
    
    UPDATE b
    SET b.Booking_Created_By = u.UserName
    FROM [dbo].[RLT_BOOKING] b
    INNER JOIN [Identity].[User] u ON CAST(b.Created_By AS NVARCHAR) = u.Id
    WHERE b.Booking_Created_By IS NULL
    
    PRINT 'Existing records updated successfully.'
    
    -- Create index for better performance
    CREATE NONCLUSTERED INDEX [IX_RLT_BOOKING_Booking_Created_By]
    ON [dbo].[RLT_BOOKING] ([Booking_Created_By])
    WHERE [Booking_Created_By] IS NOT NULL
    
    PRINT 'Index created successfully.'
END
ELSE
BEGIN
    PRINT 'Booking_Created_By column already exists.'
END
GO

-- =============================================
-- Update the stored procedure to use the new column
-- =============================================

-- Drop existing procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'usp_Booking_GetUserBookings')
BEGIN
    DROP PROCEDURE [dbo].[usp_Booking_GetUserBookings]
    PRINT 'Existing usp_Booking_GetUserBookings procedure dropped.'
END
GO

-- Create the updated stored procedure
CREATE PROCEDURE [dbo].[usp_Booking_GetUserBookings]
    @UserId NVARCHAR(50),
    @Cursor NVARCHAR(50) = NULL,
    @PageSize INT = 10
WITH RECOMPILE
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    -- Validate page size (max 10)
    IF @PageSize > 10
        SET @PageSize = 10;
    
    IF @PageSize <= 0
        SET @PageSize = 10;

    -- Parse cursor date
    DECLARE @CursorDate DATETIME = NULL;
    IF @Cursor IS NOT NULL AND @Cursor != ''
    BEGIN
        BEGIN TRY
            SET @CursorDate = CAST(@Cursor AS DATETIME);
        END TRY
        BEGIN CATCH
            SET @CursorDate = NULL;
        END CATCH
    END

    -- Get bookings with pagination using the new column
    WITH BookingData AS (
        SELECT
            b.Booking_Id as BookingId,
            cf.CitY_Name as PickUpCity,
            ct.CitY_Name as DropOffCity,
            tt.Trip_Type as TripType,
            cc.Car_Category_Abbr as CarCategory,
            ROUND(b.Fare, 0) as Fare,
            b.PickUp_Address as PickUpAddress,
            b.DropOff_Address as DropOffAddress,
            b.PickUp_Date as PickUpDate,
            b.PickUp_Time as PickUpTime,
            b.[Name] as TravelerName,
            b.Mobile_No1 as PhoneNumber,
            b.razorpay_status as RazorpayStatus,
            b.Created_Date as BookingDate,
            ROW_NUMBER() OVER (ORDER BY b.Created_Date DESC) as RowNum
        FROM [dbo].[RLT_BOOKING] b
        INNER JOIN [dbo].[RLT_CITY] cf ON b.City_From_Id = cf.PKID
        INNER JOIN [dbo].[RLT_CITY] ct ON b.City_To_Id = ct.PKID
        INNER JOIN [dbo].[RLT_TRIP_TYPES] tt ON b.Trip_Type_Id = tt.PKID
        INNER JOIN [dbo].[RLT_CAR_CATEGORY] cc ON b.Car_Category_Id = cc.PKID
        WHERE b.Booking_Created_By = @UserId  -- Using the new column
        AND (@CursorDate IS NULL OR b.Created_Date < @CursorDate)
    )
    SELECT TOP (@PageSize + 1) *
    FROM BookingData
    ORDER BY BookingDate DESC;
END
GO

PRINT 'Schema update completed successfully!'
PRINT 'New column: Booking_Created_By added to RLT_BOOKING table'
PRINT 'Updated stored procedure: usp_Booking_GetUserBookings created'
