﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltClassMaster
    {
        public int Pkid { get; set; }
        public string ClassName { get; set; }
        public double? ClassChargesPkms { get; set; }
        public int? WaitingCharges { get; set; }
        public int? DriverNightCharges { get; set; }
        public int? OtherCharges { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}
