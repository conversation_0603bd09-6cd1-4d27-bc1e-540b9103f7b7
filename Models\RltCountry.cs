﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltCountry
    {
        public RltCountry()
        {
            RltState = new HashSet<RltState>();
        }

        public int Pkid { get; set; }
        public Guid? PkGuid { get; set; }
        public string CountryName { get; set; }
        public string CounrtyAbbr { get; set; }
        public bool IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }

        public virtual ICollection<RltState> RltState { get; set; }
    }
}
