﻿using Application.DTOs.Payment;
using Application.Exceptions;
using Application.Interfaces.Repositories;
using Application.Wrappers;
using AutoMapper;
using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;
using Application.Interfaces;
using Application.DTOs.Email;

namespace Application.Features.Bookings.Command
{
    public class CreateBookingPaymentCommand : IRequest<Response<RazorPaymentResponse>>
    {
        public string PaymentId { get; set; }
        public string RazorpayOrderId { get; set; }
        public string RazorpaySignature { get; set; }


        public class CreateBookingPaymentCommandHandler : IRequestHandler<CreateBookingPaymentCommand, Response<RazorPaymentResponse>>
        {
            private readonly IBookingRepositoryAsync _bookingRepositoryAsync;
            private readonly IMapper _mapper;
            private readonly IEmailService _emailService;

            public CreateBookingPaymentCommandHandler(
                IBookingRepositoryAsync bookingRepositoryAsync,
                IMapper mapper,
                IEmailService emailService)
            {
                _bookingRepositoryAsync = bookingRepositoryAsync;
                _mapper = mapper;
                _emailService = emailService;
            }

            public async Task<Response<RazorPaymentResponse>> Handle(CreateBookingPaymentCommand request, CancellationToken cancellationToken)
            {
                var booking = await _bookingRepositoryAsync.RazorPayPaymentSuccess(request.PaymentId, request.RazorpayOrderId, request.RazorpaySignature);
                if (booking == null)
                {
                    throw new ApiException($"Payment Failed.");
                }
                else
                {
                    Console.WriteLine($"Payment Success BookingId: {booking.BookingId}");
                    // Get booking details to include in the email
                    var bookingDetails = await _bookingRepositoryAsync.GetByUniqueIdAsync(booking.BookingId);
                    Console.WriteLine($"Booking Details: {bookingDetails}");

                    if (bookingDetails != null)
                    {
                        // Send confirmation email
                        await SendBookingConfirmationEmail(bookingDetails, booking.TransactionId);
                    }

                    return new Response<RazorPaymentResponse>(booking);
                }
            }

            private async Task SendBookingConfirmationEmail(Domain.Entities.RLT_BOOKING bookingDetails, string transactionId)
            {
                if (string.IsNullOrEmpty(bookingDetails.MailId))
                    return;

                string emailBody = $@"
                <h2>Booking Confirmation</h2>
                <p>Dear {bookingDetails.TravelerName},</p>
                <p>Thank you for booking with CabYaari. Your booking has been confirmed!</p>
                
                <h3>Booking Details:</h3>
                <ul>
                    <li><strong>Booking ID:</strong> {bookingDetails.BookingID}</li>
                    <li><strong>Transaction ID:</strong> {transactionId}</li>
                    <li><strong>Trip Type:</strong> {bookingDetails.TripType}</li>
                    <li><strong>From:</strong> {bookingDetails.PickUpCity}</li>
                    <li><strong>To:</strong> {bookingDetails.DropOffCity}</li>
                    <li><strong>Pickup Date:</strong> {bookingDetails.PickUpTime}</li>
                    <li><strong>Car Category:</strong> {bookingDetails.CarCategory}</li>
                    <li><strong>Total Fare:</strong> ₹{bookingDetails.Fare}</li>
                </ul>
                
                <p>For any assistance, please contact our customer support.</p>
                <p>Thank you for choosing CabYaari!</p>
                ";

                var emailRequest = new EmailRequest
                {
                    From = "<EMAIL>",
                    To = bookingDetails.MailId,
                    Subject = $"CabYaari Booking Confirmation - {bookingDetails.BookingID}",
                    Body = emailBody
                };

                await _emailService.SendAsync(emailRequest);
            }
        }
    }
}