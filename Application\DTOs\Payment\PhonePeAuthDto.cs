using System;
using System.Text.Json.Serialization;

namespace Application.DTOs.Payment
{
    public class PhonePeAuthRequest
    {
        [JsonPropertyName("client_id")]
        public string ClientId { get; set; }

        [JsonPropertyName("client_version")]
        public string ClientVersion { get; set; }

        [JsonPropertyName("client_secret")]
        public string ClientSecret { get; set; }

        [JsonPropertyName("grant_type")]
        public string GrantType { get; set; } = "client_credentials";
    }

    public class PhonePeAuthResponse
    {
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; }

        [JsonPropertyName("encrypted_access_token")]
        public string EncryptedAccessToken { get; set; }

        [JsonPropertyName("expires_in")]
        public int? ExpiresIn { get; set; }

        [JsonPropertyName("issued_at")]
        public long IssuedAt { get; set; }

        [JsonPropertyName("expires_at")]
        public long ExpiresAt { get; set; }

        [JsonPropertyName("session_expires_at")]
        public long SessionExpiresAt { get; set; }

        [JsonPropertyName("token_type")]
        public string TokenType { get; set; }
    }

    public class PhonePePaymentInitiateRequest
    {
        [JsonPropertyName("merchantOrderId")]
        public string MerchantOrderId { get; set; }

        [JsonPropertyName("amount")]
        public long Amount { get; set; }

        [JsonPropertyName("expireAfter")]
        public int ExpireAfter { get; set; } = 1200; // 20 minutes in seconds

        [JsonPropertyName("metaInfo")]
        public PhonePeMetaInfo MetaInfo { get; set; }

        [JsonPropertyName("paymentFlow")]
        public PhonePePaymentFlow PaymentFlow { get; set; }
    }

    public class PhonePeMetaInfo
    {
        [JsonPropertyName("udf1")]
        public string Udf1 { get; set; }

        [JsonPropertyName("udf2")]
        public string Udf2 { get; set; }

        [JsonPropertyName("udf3")]
        public string Udf3 { get; set; }

        [JsonPropertyName("udf4")]
        public string Udf4 { get; set; }

        [JsonPropertyName("udf5")]
        public string Udf5 { get; set; }
    }

    public class PhonePePaymentFlow
    {
        [JsonPropertyName("type")]
        public string Type { get; set; } = "PG_CHECKOUT";

        [JsonPropertyName("message")]
        public string Message { get; set; }

        [JsonPropertyName("merchantUrls")]
        public PhonePeMerchantUrls MerchantUrls { get; set; }
    }

    public class PhonePeMerchantUrls
    {
        [JsonPropertyName("redirectUrl")]
        public string RedirectUrl { get; set; }
    }

    public class PhonePePaymentInitiateResponse
    {
        [JsonPropertyName("orderId")]
        public string OrderId { get; set; }

        [JsonPropertyName("state")]
        public string State { get; set; }

        [JsonPropertyName("expireAt")]
        public long ExpireAt { get; set; }

        [JsonPropertyName("redirectUrl")]
        public string RedirectUrl { get; set; }
    }
} 