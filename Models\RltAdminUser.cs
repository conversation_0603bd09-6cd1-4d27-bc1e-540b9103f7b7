﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltAdminUser
    {
        public int Pkid { get; set; }
        public int? RoleId { get; set; }
        public string UserName { get; set; }
        public string UserPwd { get; set; }
        public string UserPhoto { get; set; }
        public string UserFirstName { get; set; }
        public string UserLastName { get; set; }
        public string UserEmailId { get; set; }
        public string UserMobileNo { get; set; }
        public int? VendorId { get; set; }
        public int? CarOwnerId { get; set; }
        public int? EmployeeId { get; set; }
        public string AadharId { get; set; }
        public int? DepartmentId { get; set; }
        public string Address { get; set; }
        public DateTime? CreatedDate { get; set; }
        public Guid? CreatedBy { get; set; }
        public bool? IsActive { get; set; }
        public int? IsAdmin { get; set; }
        public bool? IsDeleted { get; set; }
        public string Description { get; set; }
        public bool? Is2TfaAuthentication { get; set; }
    }
}
