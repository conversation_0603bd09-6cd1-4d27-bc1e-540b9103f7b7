﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltEmployee
    {
        public long Pkid { get; set; }
        public string EmployeeName { get; set; }
        public string Photo { get; set; }
        public DateTime? Dob { get; set; }
        public string Phone1 { get; set; }
        public string Phone2 { get; set; }
        public string Phone1IsWhatsup { get; set; }
        public string Email { get; set; }
        public string Gender { get; set; }
        public string MarritalStatus { get; set; }
        public string SpousesName { get; set; }
        public string FatherName { get; set; }
        public string MotherName { get; set; }
        public bool? CurrentPermanentSame { get; set; }
        public string CurrentAddress { get; set; }
        public string PermanentAddress { get; set; }
        public string PermanentAddress1 { get; set; }
        public string PermanentAddress2 { get; set; }
        public int? PermanentAddressState { get; set; }
        public int? PermanentAddressCity { get; set; }
        public string CurrentAddress1 { get; set; }
        public string CurrentAddress2 { get; set; }
        public int? CurrentAddressState { get; set; }
        public int? CurrentAddressCity { get; set; }
        public string _10thSchool { get; set; }
        public string _10thPassingYear { get; set; }
        public string _10thPercentage { get; set; }
        public string _12thSchool { get; set; }
        public string _12thPassingYear { get; set; }
        public string _12thPercentage { get; set; }
        public string DegreeName { get; set; }
        public string DegreeCollege { get; set; }
        public string DegreePassingYear { get; set; }
        public string DegreePercentage { get; set; }
        public string MasterDegreeName { get; set; }
        public string MasterDegreeCollege { get; set; }
        public string MasterDegreePassingYear { get; set; }
        public string MasterDegreePercentage { get; set; }
        public string EmployeeId { get; set; }
        public string Department { get; set; }
        public string Designation { get; set; }
        public string Supervisor { get; set; }
        public int? WorkCity { get; set; }
        public string WorkPhone { get; set; }
        public string WorkEmail { get; set; }
        public DateTime? WorkStartDate { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public int? UpdatedBy { get; set; }
    }
}
