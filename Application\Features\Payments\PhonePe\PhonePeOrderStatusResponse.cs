﻿using System.Collections.Generic;

namespace Application.Features.Payments.PhonePe
{
    public class PhonePeOrderStatusResponse
    {
        public string orderId { get; set; }
        public string state { get; set; }
        public long amount { get; set; }
        public long expireAt { get; set; }
        public MetaInfo metaInfo { get; set; }
        public List<PaymentDetail> paymentDetails { get; set; }
    }

    public class MetaInfo
    {
        public string udf1 { get; set; }
        public string udf2 { get; set; }
        public string udf3 { get; set; }
        public string udf4 { get; set; }
        public string udf5 { get; set; }
    }

    public class PaymentDetail
    {
        public string paymentMode { get; set; }
        public string transactionId { get; set; }
        public long timestamp { get; set; }
        public long amount { get; set; }
        public string state { get; set; }
        public List<SplitInstrument> splitInstruments { get; set; }
    }

    public class SplitInstrument
    {
        public long amount { get; set; }
        public Rail rail { get; set; }
        public Instrument instrument { get; set; }
    }

    public class Rail
    {
        public string type { get; set; }
        public string transactionId { get; set; }
        public string authorizationCode { get; set; }
        public string serviceTransactionId { get; set; }
    }

    public class Instrument
    {
        public string type { get; set; }
        public string bankTransactionId { get; set; }
        public string bankId { get; set; }
        public string arn { get; set; }
        public string brn { get; set; }
    }

}
