﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltLocationCode
    {
        public long Pkid { get; set; }
        public string ZipCode { get; set; }
        public Guid? PkGuid { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatdedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public int? CityPkid { get; set; }
        public string LocationName { get; set; }

        public virtual RltCity CityPk { get; set; }
    }
}
