﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltDriverEnquiries
    {
        public int Pkid { get; set; }
        public string DriverName { get; set; }
        public string DriverPhone1 { get; set; }
        public string DriverCarNo { get; set; }
        public string DriverMail { get; set; }
        public string DriverAddress { get; set; }
        public DateTime? RequestDate { get; set; }
        public int? DriverApprovingStatus { get; set; }
        public string Remark { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public int? UpdatedBy { get; set; }
        public bool? IsWhatsAppNumber { get; set; }
    }
}
