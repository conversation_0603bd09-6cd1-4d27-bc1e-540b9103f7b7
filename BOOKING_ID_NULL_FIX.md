# Booking ID Null Issue Fix

## Problem Identified
The payment verification API response shows `"bookingId": null` even though the payment was successful:

```json
{
    "succeeded": true,
    "message": "Payment verification completed",
    "data": {
        "paymentStatus": "Paid",
        "bookingId": null,  // ❌ This should not be null
        "amount": 5796.00,
        "paymentType": "FULL"
    }
}
```

## Root Cause Analysis

### 1. **Flow Trace**
1. **Token Generation**: Sets `MerchantTransactionId = bookingResponse.BookingId`
2. **Verification**: Calls `GetByUniqueIdAsync(request.VerifyRequest.MerchantTransactionId)`
3. **Repository**: Calls stored procedure `usp_BookingDetails_Get`
4. **Issue**: The stored procedure either doesn't exist or has incorrect field mapping

### 2. **Field Mapping Issue**
- **Entity Property**: `RLT_BOOKING.BookingID` (C# property)
- **Database Column**: `RLT_BOOKING.Booking_Id` (SQL column)
- **Problem**: The stored procedure wasn't mapping `Booking_Id` to `BookingID` correctly

### 3. **Missing Stored Procedure**
The `GetByUniqueIdAsync` method calls `usp_BookingDetails_Get` but this stored procedure was missing or incomplete.

## Solution Implemented

### 1. **Created usp_BookingDetails_Get Stored Procedure**

```sql
CREATE PROCEDURE [dbo].[usp_BookingDetails_Get]
    @bookingID nvarchar(30)
AS
BEGIN
    SELECT 
        Booking_Id AS BookingID,  -- ✅ Proper field mapping
        PickUp_City AS PickUpCity,
        DropOff_City AS DropOffCity,
        Trip_Type AS TripType,
        Car_Category AS CarCategory,
        -- ... all other fields with proper mapping
        PaymentType,              -- ✅ New partial payment fields
        PartialPaymentAmount,
        RemainingAmountForDriver
    FROM RLT_BOOKING 
    WHERE Booking_Id = @bookingID
END
```

### 2. **Key Features of the Fix**

#### **Proper Field Mapping**
- Maps database column names to C# property names
- Example: `Booking_Id AS BookingID`, `PickUp_City AS PickUpCity`

#### **Includes New Partial Payment Fields**
- `PaymentType`
- `PartialPaymentAmount` 
- `RemainingAmountForDriver`

#### **Complete Field Coverage**
- Maps all existing fields from the RLT_BOOKING table
- Ensures no data is lost during retrieval

## Expected Result After Fix

### **Before Fix:**
```json
{
    "bookingId": null,  // ❌ Null because of missing/incorrect stored procedure
    "paymentStatus": "Paid"
}
```

### **After Fix:**
```json
{
    "bookingId": "BK2507051711135",  // ✅ Correct booking ID returned
    "paymentStatus": "Paid",
    "paymentType": "FULL",
    "partialPaymentAmount": null,
    "remainingAmountForDriver": null
}
```

## Verification Steps

### 1. **Deploy the Database Changes**
Execute the `DATABASE_SCHEMA_CHANGES.sql` to create the missing stored procedure.

### 2. **Test Payment Verification**
Make a payment and verify the response includes the correct booking ID:

```bash
curl -X POST "https://devapi.cabyaari.com/api/v1/payments/phonepe/verify" \
-H "Content-Type: application/json" \
-d '{
    "merchantTransactionId": "BK2507051711135",
    "orderId": "OMO2507051711135059258542"
}'
```

### 3. **Check Logs**
Verify the booking lookup is successful:
```
[VerifyPhonePePayment] Booking lookup result - Found: True, BookingId: BK2507051711135
```

## Files Updated

1. **`DATABASE_SCHEMA_CHANGES.sql`** - Added `usp_BookingDetails_Get` stored procedure
2. **`STORED_PROCEDURES_V2.sql`** - Added the same stored procedure for reference
3. **`BOOKING_ID_NULL_FIX.md`** - This documentation

## Database Objects Created

### **New Stored Procedure:**
- `usp_BookingDetails_Get` - Retrieves booking details with proper field mapping

### **Field Mapping Reference:**
| Database Column | C# Property | Description |
|----------------|-------------|-------------|
| Booking_Id | BookingID | Primary booking identifier |
| PickUp_City | PickUpCity | Pickup city name |
| DropOff_City | DropOffCity | Drop-off city name |
| Trip_Type | TripType | Type of trip |
| Car_Category | CarCategory | Vehicle category |
| PaymentType | PaymentType | PARTIAL or FULL |
| PartialPaymentAmount | PartialPaymentAmount | Amount paid online |
| RemainingAmountForDriver | RemainingAmountForDriver | Amount for driver |

## Impact

### ✅ **Benefits:**
1. **Correct Booking ID**: Payment verification will return the actual booking ID
2. **Complete Data**: All booking fields will be properly retrieved
3. **Partial Payment Support**: New payment fields are included
4. **Better Debugging**: Proper field mapping makes troubleshooting easier

### 🔧 **What This Fixes:**
- Null booking ID in payment verification responses
- Missing booking data during payment verification
- Incomplete field mapping between database and application
- Support for new partial payment fields in booking retrieval

This fix ensures that the payment verification API returns complete and accurate booking information, including the correct booking ID and partial payment details.
