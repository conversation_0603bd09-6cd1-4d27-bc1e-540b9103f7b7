﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltVendorCarDetails
    {
        public RltVendorCarDetails()
        {
            VendorCarDriverDocs = new HashSet<VendorCarDriverDocs>();
        }

        public int Pkid { get; set; }
        public int CarCompanyRef { get; set; }
        public int CarSegmentRef { get; set; }
        public int CarFuelTypeRef { get; set; }
        public string CarNumber { get; set; }
        public DateTime? CarRegistrationDate { get; set; }
        public string DriverName { get; set; }
        public string DriverPhone1 { get; set; }
        public string DriverPhone2 { get; set; }
        public int VendorRefId { get; set; }
        public bool IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public int LastModifiedBy { get; set; }

        public virtual RltCarCompany CarCompanyRefNavigation { get; set; }
        public virtual RltCarFuelTypes CarFuelTypeRefNavigation { get; set; }
        public virtual RltCarCategory CarSegmentRefNavigation { get; set; }
        public virtual RltVendorDetails VendorRef { get; set; }
        public virtual ICollection<VendorCarDriverDocs> VendorCarDriverDocs { get; set; }
    }
}
