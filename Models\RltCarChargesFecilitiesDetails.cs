﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltCarChargesFecilitiesDetails
    {
        public int Pkid { get; set; }
        public int? CarCategoryPkid { get; set; }
        public double? CarChargesPkm { get; set; }
        public double? CarWaitingChargesPm { get; set; }
        public double? CarDriverChargesPd { get; set; }
        public bool? IsContainAc { get; set; }
        public string TotalNumberSeats { get; set; }
        public bool? IsSmokingAllow { get; set; }
        public bool? IsLuggageAllow { get; set; }
        public bool? IsPetAllow { get; set; }
        public string InclusiveChargesComment { get; set; }
        public string ExtrasChargesComment { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }

        public virtual RltCarCategory CarCategoryPk { get; set; }
    }
}
