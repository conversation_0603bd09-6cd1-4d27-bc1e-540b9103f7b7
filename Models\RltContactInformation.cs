﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltContactInformation
    {
        public int Pkid { get; set; }
        public string Address { get; set; }
        public string SalesTelNo { get; set; }
        public string BusinessEmailNo { get; set; }
        public string WorkingHours { get; set; }
        public string GpscordinationLatitude { get; set; }
        public string GpscordinationLongitude { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreateDate { get; set; }
        public string SalesEmailNo { get; set; }
        public string BusinessTelNo { get; set; }
        public string GoogleMapLink { get; set; }
        public string Fblink { get; set; }
        public string TwitterLink { get; set; }
        public string InstagramLink { get; set; }
        public string PinsterLink { get; set; }
        public string GooglePlusLink { get; set; }
    }
}
