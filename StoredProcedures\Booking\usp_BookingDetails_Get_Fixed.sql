-- =============================================
-- Fixed Stored Procedure: usp_BookingDetails_Get
-- Description: Returns Booking_Created_By instead of Created_By for proper ownership validation
-- =============================================

USE [CabYaari]
GO

ALTER PROCEDURE [dbo].[usp_BookingDetails_Get]
    @bookingID nvarchar(30)
WITH RECOMPILE
AS
BEGIN
    -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    SELECT 
        b.PKID,
        b.Booking_Id AS BookingID,
        cf.CitY_Name AS PickUpCity,
        ct.CitY_Name AS DropOffCity,
        tt.Trip_Type AS TripType,
        cc.Car_Category_Abbr AS CarCategory,
        b.<PERSON>,
        b.<PERSON>,
        b.<PERSON>_Fare AS BasicFare,
        b.Driver_Charge AS DriverCharge,
        b.GST AS Gst,
        b.Fare,
        b.GST_Fare AS GstFare,
        b.Coupon_Code AS CouponCode,
        b.Coupon_Discount AS CouponDiscount,
        b.Booking_Date AS BookingDate,
        b.PickUp_Address AS PickUpAddress,
        b.DropOff_Address AS DropOffAddress,
        b.PickUp_Date AS PickUpDate,
        b.PickUp_Time AS PickUpTime,
        b.[Name] AS TravelerName,
        b.Mobile_No1 AS PhoneNumber,
        b.Mail_Id AS MailId,
        b.Mode_Of_Payment_Id AS PaymentMode,
        b.Booking_Status_Id AS BookingStatusId,
        -- ✅ FIXED: Return actual user identifier instead of Created_By
        ISNULL(b.Booking_Created_By, CAST(b.Created_By AS NVARCHAR)) AS BookingCreatedBy,
        b.razorpay_payment_id AS RazorpayPaymentId,
        b.razorpay_order_id AS RazorpayOrderid,
        b.razorpay_signature AS RazorpaySignature,
        b.razorpay_status AS RazorpayStatus,
        b.PickUpAddressLatitude AS PickUpAddressLongLat,
        b.PickUpAddressLongitude AS DropOffAddressLongLat,
        b.CashAmountToPayDriver,
        b.PaymentOption,
        b.TollCharge,
        b.PaymentType,
        b.PartialPaymentAmount,
        b.RemainingAmountForDriver
    FROM 
        [dbo].[RLT_BOOKING] b
    INNER JOIN 
        [dbo].[RLT_CITY] cf ON b.City_From_Id = cf.PKID
    INNER JOIN 
        [dbo].[RLT_CITY] ct ON b.City_To_Id = ct.PKID
    INNER JOIN 
        [dbo].[RLT_TRIP_TYPES] tt ON b.Trip_Type_Id = tt.PKID
    INNER JOIN 
        [dbo].[RLT_CAR_CATEGORY] cc ON b.Car_Category_Id = cc.PKID
    WHERE 
        b.Booking_Id = @bookingID
END
GO

PRINT 'usp_BookingDetails_Get fixed to return proper BookingCreatedBy field!'

-- =============================================
-- What this fix does:
-- 
-- OLD: b.Created_By AS BookingCreatedBy  (returns "1")
-- NEW: ISNULL(b.Booking_Created_By, CAST(b.Created_By AS NVARCHAR)) AS BookingCreatedBy
-- 
-- This will return:
-- - For new bookings: The actual GUID from Booking_Created_By
-- - For old bookings: The Created_By value as fallback
-- 
-- This allows proper ownership validation in the API
-- =============================================
