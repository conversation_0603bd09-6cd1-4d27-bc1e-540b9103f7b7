﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Domain.Entities
{
    public class Rlt_Car_Category
    {
        [Key]
        public int PKID { get; set; }
        public Guid? PkGuid { get; set; }
        public string Car_Category_Name { get; set; }
        public string Car_Category_Abbr { get; set; }
        public double? Per_Km_Fare { get; set; }
        public decimal? Base_Fare { get; set; }
        public int? Capacity { get; set; }
        public string Features { get; set; }
        public bool? Is_Active { get; set; }
        public DateTime? Created_Date { get; set; }
        public DateTime? Updated_Date { get; set; }
        public string Car_Categroy_Image { get; set; }
    }
}
