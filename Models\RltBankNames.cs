﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltBankNames
    {
        public RltBankNames()
        {
            RltCarOwnerBankDetails = new HashSet<RltCarOwnerBankDetails>();
        }

        public int Pkid { get; set; }
        public Guid? PkGuid { get; set; }
        public string BankName { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }

        public virtual ICollection<RltCarOwnerBankDetails> RltCarOwnerBankDetails { get; set; }
    }
}
