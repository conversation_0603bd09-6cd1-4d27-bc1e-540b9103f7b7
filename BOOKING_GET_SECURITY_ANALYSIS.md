# Booking Get Endpoint Security Analysis

## Current Implementation Issues

### 🚨 **CRITICAL SECURITY VULNERABILITIES**

#### 1. **No Authorization Check**
```csharp
[HttpGet("{bookingId}")]
public async Task<IActionResult> Get(string bookingId)  // ❌ No [Authorize] attribute
{
    return Ok(await Mediator.Send(new GetBookingByIdQuery { BookingId = bookingId }));
}
```

**Risk**: Anyone can access any booking by guessing booking IDs.

#### 2. **No Ownership Validation**
The endpoint doesn't verify if the requesting user owns the booking.

**Risk**: Users can access other users' booking details.

#### 3. **Sensitive Data Exposure**
The endpoint returns the complete `RLT_BOOKING` entity including:
- `RazorpayPaymentId` - Payment gateway transaction ID
- `RazorpaySignature` - Payment signature (security sensitive)
- `PhoneNumber` - Personal information
- `MailId` - Personal information
- `BookingCreatedBy` - Internal user ID
- `PickUpAddressLongLat` - Exact location coordinates

#### 4. **No Input Validation**
No validation on the `bookingId` parameter.

## API Call Information

### **Current Endpoint**
```
GET /api/v1/booking/{bookingId}
```

### **Example Call**
```bash
curl -X GET "https://devapi.cabyaari.com/api/v1/booking/CY-050725-57374"
```

### **Response (Current - Problematic)**
```json
{
    "succeeded": true,
    "data": {
        "bookingID": "CY-050725-57374",
        "travelerName": "John Doe",
        "phoneNumber": "9876543210",        // ❌ Sensitive
        "mailId": "<EMAIL>",       // ❌ Sensitive
        "razorpayPaymentId": "pay_xyz123",  // ❌ Sensitive
        "razorpaySignature": "signature",   // ❌ Very Sensitive
        "bookingCreatedBy": "user123",      // ❌ Internal ID
        "pickUpAddressLongLat": "26.9124,75.7873", // ❌ Exact location
        // ... all other booking data
    }
}
```

## Recommended Fixes

### 1. **Add Authorization and Ownership Validation**

```csharp
[HttpGet("{bookingId}")]
[Authorize]  // ✅ Add authorization
public async Task<IActionResult> Get(string bookingId)
{
    // ✅ Get authenticated user ID
    var userId = User.FindFirst("uid")?.Value;
    
    if (string.IsNullOrEmpty(userId))
    {
        return Unauthorized(new { Message = "User ID not found in token" });
    }

    // ✅ Validate input
    if (string.IsNullOrWhiteSpace(bookingId))
    {
        return BadRequest(new { Message = "Booking ID is required" });
    }

    try
    {
        var query = new GetBookingByIdQuery 
        { 
            BookingId = bookingId,
            RequestingUserId = userId  // ✅ Pass user ID for ownership check
        };
        
        var result = await Mediator.Send(query);
        return Ok(result);
    }
    catch (UnauthorizedAccessException)
    {
        return Forbid(new { Message = "You don't have access to this booking" });
    }
    catch (Exception ex)
    {
        return StatusCode(500, new { Message = "Error retrieving booking" });
    }
}
```

### 2. **Create Secure Booking Response DTO**

```csharp
public class BookingDetailsResponse
{
    public string BookingID { get; set; }
    public string PickUpCity { get; set; }
    public string DropOffCity { get; set; }
    public string TripType { get; set; }
    public string CarCategory { get; set; }
    public string Duration { get; set; }
    public decimal? Distance { get; set; }
    public decimal? Fare { get; set; }
    public string PickUpAddress { get; set; }
    public string DropOffAddress { get; set; }
    public DateTime? PickUpDate { get; set; }
    public string PickUpTime { get; set; }
    public string TravelerName { get; set; }
    public string PaymentStatus { get; set; }
    public string PaymentType { get; set; }
    public decimal? PartialPaymentAmount { get; set; }
    public decimal? RemainingAmountForDriver { get; set; }
    
    // ❌ EXCLUDED sensitive fields:
    // - PhoneNumber
    // - MailId  
    // - RazorpayPaymentId
    // - RazorpaySignature
    // - BookingCreatedBy
    // - PickUpAddressLongLat (exact coordinates)
}
```

### 3. **Update Query Handler with Ownership Check**

```csharp
public class GetBookingByIdQuery : IRequest<Response<BookingDetailsResponse>>
{
    public string BookingId { get; set; }
    public string RequestingUserId { get; set; }  // ✅ Add user ID
}

public class GetBookingByIdQueryHandler : IRequestHandler<GetBookingByIdQuery, Response<BookingDetailsResponse>>
{
    public async Task<Response<BookingDetailsResponse>> Handle(GetBookingByIdQuery query, CancellationToken cancellationToken)
    {
        var booking = await _bookingRepositoryAsync.GetByUniqueIdAsync(query.BookingId);
        
        if (booking == null) 
            throw new ApiException("Booking Not Found.");
        
        // ✅ Ownership validation
        if (booking.BookingCreatedBy != query.RequestingUserId)
            throw new UnauthorizedAccessException("You don't have access to this booking");
        
        // ✅ Map to secure DTO
        var response = _mapper.Map<BookingDetailsResponse>(booking);
        return new Response<BookingDetailsResponse>(response);
    }
}
```

## Secure API Usage

### **Frontend Implementation**
```javascript
// ✅ Secure API call with authentication
const getBookingDetails = async (bookingId) => {
    try {
        const response = await fetch(`/api/v1/booking/${bookingId}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,  // ✅ Required
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            if (response.status === 401) {
                // Redirect to login
                window.location.href = '/login';
                return;
            }
            if (response.status === 403) {
                alert('You don\'t have access to this booking');
                return;
            }
            throw new Error('Failed to fetch booking details');
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching booking:', error);
        throw error;
    }
};
```

## Additional Security Measures

### 1. **Rate Limiting**
```csharp
[EnableRateLimiting("BookingPolicy")]  // Limit requests per user
[HttpGet("{bookingId}")]
```

### 2. **Input Validation**
```csharp
[HttpGet("{bookingId:regex(^CY-[0-9]{{6}}-[0-9]{{5}}$)}")]  // Validate booking ID format: CY-DDMMYY-NNNNN
```

### 3. **Audit Logging**
```csharp
// Log booking access attempts
_logger.LogInformation("User {UserId} accessed booking {BookingId}", userId, bookingId);
```

## Files to Update

1. **`BookingController.cs`** - Add authorization and validation
2. **`GetBookingByIdQuery.cs`** - Add ownership validation
3. **Create `BookingDetailsResponse.cs`** - Secure response DTO
4. **Update AutoMapper profile** - Map to secure DTO

## Testing Checklist

- [ ] Unauthenticated requests return 401
- [ ] Users cannot access other users' bookings
- [ ] Sensitive data is not exposed in response
- [ ] Invalid booking IDs return appropriate errors
- [ ] Rate limiting works correctly

This security analysis reveals critical vulnerabilities that need immediate attention to protect user data and prevent unauthorized access.
