using Application.DTOs.Payment;
using Application.Features.Bookings.CreateBooking;
using Application.Features.Payments.PhonePe;
using Application.Wrappers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System.Text.Json;
using System;
using System.Linq;

namespace WebApi.Controllers.v1
{
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/payments/phonepe")]
    public class PhonePePaymentController : BaseApiController
    {
        [HttpPost("token")]
        [Authorize]
        public async Task<IActionResult> GeneratePaymentToken([FromBody] BookingCommand command)
        {
            // Get authenticated user information
            var isAuthenticated = User.Identity.IsAuthenticated;
            var userName = User.Identity.Name;
            var userId = User.FindFirst("uid")?.Value ?? User.FindFirst("sub")?.Value;

            Console.WriteLine($"PhonePe Token - Is Authenticated: {isAuthenticated}");
            Console.WriteLine($"PhonePe Token - User Name: {userName}");
            Console.WriteLine($"PhonePe Token - User ID: {userId}");

            // Set the BookingCreatedBy property with the authenticated user's information
            if (!string.IsNullOrEmpty(userName))
            {
                command.BookingCreatedBy = userName;  // Use username for stored procedure lookup
            }
            else if (!string.IsNullOrEmpty(userId))
            {
                command.BookingCreatedBy = userId;    // Fallback to UID
            }

            Console.WriteLine($"PhonePe Token - BookingCreatedBy set to: {command.BookingCreatedBy}");

            var generateTokenCommand = new GeneratePhonePeTokenCommand
            {
                BookingCommand = command
            };

            var result = await Mediator.Send(generateTokenCommand);
            return Ok(result);
        }

        [HttpPost("verify")]
        public async Task<IActionResult> VerifyPayment([FromBody] PhonePeVerifyRequest request)
        {
            Console.WriteLine($"[PhonePePaymentController] VerifyPayment Request Payload:");
            Console.WriteLine($"[PhonePePaymentController] PhonePeVerifyRequest: {JsonSerializer.Serialize(request, new JsonSerializerOptions { WriteIndented = true })}");
            Console.WriteLine($"[PhonePePaymentController] Query Parameters: {JsonSerializer.Serialize(Request.Query.ToDictionary(q => q.Key, q => q.Value.ToString()), new JsonSerializerOptions { WriteIndented = true })}");

            var verifyCommand = new VerifyPhonePePaymentCommand
            {
                VerifyRequest = request
            };

            var result = await Mediator.Send(verifyCommand);
            return Ok(result);
        }

        [HttpPost("callback")]
        public async Task<IActionResult> PaymentCallback([FromBody] PhonePeVerifyRequest request)
        {
            Console.WriteLine($"[PhonePePaymentController] PaymentCallback Request Payload:");
            Console.WriteLine($"[PhonePePaymentController] PhonePeVerifyRequest: {JsonSerializer.Serialize(request, new JsonSerializerOptions { WriteIndented = true })}");
            Console.WriteLine($"[PhonePePaymentController] Query Parameters: {JsonSerializer.Serialize(Request.Query, new JsonSerializerOptions { WriteIndented = true })}");
            Console.WriteLine($"[PhonePePaymentController] Headers: {JsonSerializer.Serialize(Request.Headers, new JsonSerializerOptions { WriteIndented = true })}");

            // This endpoint will be called by PhonePe after payment completion
            var verifyCommand = new VerifyPhonePePaymentCommand
            {
                VerifyRequest = request
            };

            var result = await Mediator.Send(verifyCommand);
            return Ok(result);
        }
    }
} 