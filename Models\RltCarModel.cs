﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltCarModel
    {
        public int Pkid { get; set; }
        public Guid? PkGuid { get; set; }
        public string CarModelName { get; set; }
        public int? CarCategoryPkid { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public int? CarFuelTypeStatus { get; set; }
        public int? CarCompanyPkid { get; set; }
        public int? CarSegmentPkid { get; set; }

        public virtual RltCarCategory CarCategoryPk { get; set; }
        public virtual RltCarCompany CarCompanyPk { get; set; }
        public virtual RltCarFuelTypes CarFuelTypeStatusNavigation { get; set; }
    }
}
