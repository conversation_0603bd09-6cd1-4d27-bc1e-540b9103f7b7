﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltCarSegment
    {
        public int Pkid { get; set; }
        public string CarSegment { get; set; }
        public string CarSegmentDescription { get; set; }
        public double? PerKmFare { get; set; }
        public int? Capacity { get; set; }
        public string Features { get; set; }
        public bool? IsActive { get; set; }
    }
}
