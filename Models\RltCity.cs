﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltCity
    {
        public RltCity()
        {
            RltBookingPaymentDetailsPassengerDropoffCityPk = new HashSet<RltBookingPaymentDetails>();
            RltBookingPaymentDetailsPassengerPickupCityPk = new HashSet<RltBookingPaymentDetails>();
            RltLocationCode = new HashSet<RltLocationCode>();
            RltRoutesDetailsDropoffCityPk = new HashSet<RltRoutesDetails>();
            RltRoutesDetailsPickupCityPk = new HashSet<RltRoutesDetails>();
        }

        public int Pkid { get; set; }
        public Guid? PkGuid { get; set; }
        public string CityName { get; set; }
        public string CityAbbr { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public int? StatePkid { get; set; }
        public string Latitude { get; set; }
        public string Longitude { get; set; }
        public string ELoc { get; set; }
        public int? OrderIndex { get; set; }
        public string Score { get; set; }

        public virtual RltState StatePk { get; set; }
        public virtual ICollection<RltBookingPaymentDetails> RltBookingPaymentDetailsPassengerDropoffCityPk { get; set; }
        public virtual ICollection<RltBookingPaymentDetails> RltBookingPaymentDetailsPassengerPickupCityPk { get; set; }
        public virtual ICollection<RltLocationCode> RltLocationCode { get; set; }
        public virtual ICollection<RltRoutesDetails> RltRoutesDetailsDropoffCityPk { get; set; }
        public virtual ICollection<RltRoutesDetails> RltRoutesDetailsPickupCityPk { get; set; }
    }
}
