﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Application.Interfaces;
using Domain.Settings;
using Infrastructure.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WebApi.Services;

namespace WebApi.Controllers.v1
{
    [ApiVersion("1.0")]
    public class MMIServicesController : BaseApiController
    {
        public MapSettings _mapSettings { get; }

        public ILogger<MapService> _logger { get; }

        public IMapService _mapService { get; }

        public MMIServicesController(IOptions<MapSettings> mapSettings, ILogger<MapService> logger, IMapService mapService)
        {
            _mapSettings = mapSettings.Value;
            _logger = logger;
            
        }


        // POST api/mmiservices/login
        [HttpPost("token")]

        public async Task<IActionResult> MMIToken()
        {
            string oauthAPI = this._mapSettings.OAuthAPI + "grant_type=" + this._mapSettings.GrantType + "&client_id=" + this._mapSettings.ClientId + "&client_secret=" + this._mapSettings.ClientSecret;

            string obj;
            using (var httpClient = new HttpClient())
            {
                using (var response = await httpClient.PostAsync(oauthAPI, null))
                {
                    string apiResponse = await response.Content.ReadAsStringAsync();
                    obj = apiResponse;
                }
            }

            return Ok(obj);
        }



        [HttpGet("textsearch")]
        public async Task<IActionResult> MMITextSearch([FromQuery] string placeName, [FromQuery] string token)
        {

            string obj;
            using (var httpClient = new HttpClient())
            {
                using (var response = await httpClient.GetAsync(this._mapSettings.TextSearchAPI + placeName + "&access_token=" + token))
                {
                    string apiResponse = await response.Content.ReadAsStringAsync();
                    obj = apiResponse;
                }
            }

            return Ok(obj);
        }

       [HttpGet("getcity")]
        public async Task<IActionResult> MMICitySearch([FromQuery] string placeName, [FromQuery] string token)
        {

            string obj;
            using (var httpClient = new HttpClient())
            {
                using (var response = await httpClient.GetAsync(this._mapSettings.GetCityAPI + placeName + "&access_token=" + token))
                {
                    string apiResponse = await response.Content.ReadAsStringAsync();
                    obj = apiResponse;
                }
            }

            return Ok(obj);
        }


        [HttpGet("autosuggest")]
        public async Task<IActionResult> MMIAutoSuggest([FromQuery] string placeName, [FromQuery] string token, string eloc)
        {

            string obj;
            using (var httpClient = new HttpClient())
            {
                using (var response = await httpClient.GetAsync(this._mapSettings.AutoSuggestAPI + placeName + "&filter=cop:" + eloc + "&access_token=" + token))
                {
                    string apiResponse = await response.Content.ReadAsStringAsync();
                    obj = apiResponse;
                }
            }

            return Ok(obj);
        }

        [HttpGet("near_by")]
        public async Task<IActionResult> MMINearBy([FromQuery] string refLocation, [FromQuery] string userName, [FromQuery] string keyword, [FromHeader] string token)
        {
            // &username = balmukand & refLocation = 28.467470,77.077518 & keywords = FINATM";
            string obj;
            using (var httpClient = new HttpClient())
            {
                using (var response = await httpClient.GetAsync(this._mapSettings.NearByAPI + "&username =" + userName + "&refLocation=" + refLocation + "&keywords=" + keyword + "&access_token=" + token))
                {
                    string apiResponse = await response.Content.ReadAsStringAsync();
                    obj = apiResponse;
                }
            }

            return Ok(obj);
        }

        [HttpGet("eta")]
        public async Task<IActionResult> MMIETACalculation([FromQuery] string fromLatLong, [FromQuery] string toLatLong)
        {
            //77.227434,28.610981;77.212021,28.616679?alternatives=true&&geometries=polyline&overview=full&exclude=&steps=true&region=ind";

            string obj;
            using (var httpClient = new HttpClient())
            {
                using (var response = await httpClient.GetAsync(this._mapSettings.EtaAPI + fromLatLong + ";" + toLatLong + "?alternatives=true&&geometries=polyline&overview=full&exclude=&steps=true&region=ind"))
                {
                    string apiResponse = await response.Content.ReadAsStringAsync();
                    obj = apiResponse;
                }
            }

            return Ok(obj);
        }

        [HttpGet("distance_matrix")]
        public async Task<IActionResult> MMIDistanceMatrix([FromQuery] string fromLatLang, [FromQuery] string toLatLang)
        {
            //77.983936,28.255904;77.05993,28.487555;77.15993,28.587555;77.264997,28.554534?sources=0;1&destinations=2;3";

            string obj;
            using (var httpClient = new HttpClient())
            {
                using (var response = await httpClient.GetAsync(this._mapSettings.DistnaceMatrixAPI + fromLatLang + ";" + toLatLang + "?sources=0;1&destinations=2;3"))
                {
                    string apiResponse = await response.Content.ReadAsStringAsync();
                    obj = apiResponse;
                }
            }

            return Ok(obj);
        }
    }
}
