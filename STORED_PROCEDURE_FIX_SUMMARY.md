# Stored Procedure Fix Summary

## Issue Found
The existing `usp_BookingDetails_Get` stored procedure has two critical issues:

1. **Missing BookingID Alias**: Selects `b.Booking_Id` but doesn't alias it as `BookingID`
2. **Missing Partial Payment Fields**: Doesn't include the new payment fields

## Current vs Required

### **Current (Problematic):**
```sql
SELECT 
    b.Booking_Id,  -- ❌ Missing AS BookingID alias
    -- ... other fields
    b.TollCharge
    -- ❌ Missing: PaymentType, PartialPaymentAmount, RemainingAmountForDriver
```

### **Required (Fixed):**
```sql
SELECT 
    b.Booking_Id AS BookingID,  -- ✅ Added AS BookingID alias
    -- ... other fields
    b.TollCharge,
    -- ✅ Added: New partial payment fields
    b.PaymentType,
    b.PartialPaymentAmount,
    b.RemainingAmountForDriver
```

## Key Changes Needed

### 1. **Fix BookingID Mapping**
**Change:**
```sql
b.Booking_Id  -- ❌ Current
```
**To:**
```sql
b.Booking_Id AS BookingID  -- ✅ Fixed
```

### 2. **Fix RazorpayOrderId Mapping**
**Change:**
```sql
b.razorpay_order_id AS RazorpayOrderId  -- ❌ Current (uppercase 'Id')
```
**To:**
```sql
b.razorpay_order_id AS RazorpayOrderid  -- ✅ Fixed (lowercase 'id')
```

### 3. **Add Partial Payment Fields**
**Add these lines after `b.TollCharge`:**
```sql
b.PaymentType,
b.PartialPaymentAmount,
b.RemainingAmountForDriver
```

## Complete Updated Stored Procedure

Execute this to fix the issue:

```sql
ALTER PROCEDURE [dbo].[usp_BookingDetails_Get]
    @bookingID nvarchar(30)
WITH RECOMPILE
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    SELECT 
        b.PKID,
        b.Booking_Id AS BookingID,  -- ✅ FIXED
        cf.CitY_Name AS PickUpCity,
        ct.CitY_Name AS DropOffCity,
        tt.Trip_Type AS TripType,
        cc.Car_Category_Abbr AS CarCategory,
        b.Duration,
        b.Distance,
        b.Basic_Fare AS BasicFare,
        b.Driver_Charge AS DriverCharge,
        b.GST AS Gst,
        b.Fare,
        b.GST_Fare AS GstFare,
        b.Coupon_Code AS CouponCode,
        b.Coupon_Discount AS CouponDiscount,
        b.Booking_Date AS BookingDate,
        b.PickUp_Address AS PickUpAddress,
        b.DropOff_Address AS DropOffAddress,
        b.PickUp_Date AS PickUpDate,
        b.PickUp_Time AS PickUpTime,
        b.[Name] AS TravelerName,
        b.Mobile_No1 AS PhoneNumber,
        b.Mail_Id AS MailId,
        b.Mode_Of_Payment_Id AS PaymentMode,
        b.Booking_Status_Id AS BookingStatusId,
        b.Created_By AS BookingCreatedBy,
        b.razorpay_payment_id AS RazorpayPaymentId,
        b.razorpay_order_id AS RazorpayOrderid,  -- ✅ FIXED
        b.razorpay_signature AS RazorpaySignature,
        b.razorpay_status AS RazorpayStatus,
        b.PickUpAddressLatitude AS PickUpAddressLongLat,
        b.PickUpAddressLongitude AS DropOffAddressLongLat,
        b.CashAmountToPayDriver,
        b.PaymentOption,
        b.TollCharge,
        -- ✅ ADDED: New partial payment fields
        b.PaymentType,
        b.PartialPaymentAmount,
        b.RemainingAmountForDriver
    FROM 
        [dbo].[RLT_BOOKING] b
    INNER JOIN 
        [dbo].[RLT_CITY] cf ON b.City_From_Id = cf.PKID
    INNER JOIN 
        [dbo].[RLT_CITY] ct ON b.City_To_Id = ct.PKID
    INNER JOIN 
        [dbo].[RLT_TRIP_TYPES] tt ON b.Trip_Type_Id = tt.PKID
    INNER JOIN 
        [dbo].[RLT_CAR_CATEGORY] cc ON b.Car_Category_Id = cc.PKID
    WHERE 
        b.Booking_Id = @bookingID
END
```

## Expected Result After Fix

### **Before Fix:**
```json
{
    "bookingId": null,  // ❌ Null due to missing alias
    "paymentType": null,
    "partialPaymentAmount": null,
    "remainingAmountForDriver": null
}
```

### **After Fix:**
```json
{
    "bookingId": "BK2507051711135",  // ✅ Correct booking ID
    "paymentType": "FULL",
    "partialPaymentAmount": null,
    "remainingAmountForDriver": null
}
```

## Files to Execute

1. **`usp_BookingDetails_Get_UPDATED.sql`** - Contains the complete fixed stored procedure
2. **First execute the schema changes** from `DATABASE_SCHEMA_CHANGES.sql` to add the new columns
3. **Then execute the updated stored procedure** to fix the field mapping

## Verification

After executing the fix, test the payment verification API and check that:
1. `bookingId` is no longer null
2. `paymentType` shows the correct value ("PARTIAL" or "FULL")
3. Partial payment amounts are correctly returned when applicable

This fix resolves the null booking ID issue by ensuring proper field mapping between the database and the C# entity.
