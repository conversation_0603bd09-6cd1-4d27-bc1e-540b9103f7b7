﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltBookingRules
    {
        public int Pkid { get; set; }
        public int? TripTypeId { get; set; }
        public int? CarCategoryId { get; set; }
        public int? DistanceFrom { get; set; }
        public int? DistanceTo { get; set; }
        public int? NewDistance { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public int? UpdatedBy { get; set; }
    }
}
