-- =============================================
-- Schema Fix: Add Booking_Created_By Column and Update Stored Procedure
-- Description: Adds the missing Booking_Created_By column and fixes the stored procedure
-- Author: CabYaari Development Team
-- Created: 2025-07-06
-- =============================================

USE [CabYaari]
GO

-- Step 1: Add the missing Booking_Created_By column
PRINT 'Adding Booking_Created_By column to RLT_BOOKING table...'

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RLT_BOOKING]') AND name = 'Booking_Created_By')
BEGIN
    ALTER TABLE [dbo].[RLT_BOOKING]
    ADD Booking_Created_By NVARCHAR(450) NULL
    
    PRINT 'Booking_Created_By column added successfully.'
END
ELSE
BEGIN
    PRINT 'Booking_Created_By column already exists.'
END
GO

-- Step 2: Create index for better performance
PRINT 'Creating index for Booking_Created_By column...'

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RLT_BOOKING_Booking_Created_By')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_RLT_BOOKING_Booking_Created_By]
    ON [dbo].[RLT_BOOKING] ([Booking_Created_By])
    WHERE [Booking_Created_By] IS NOT NULL
    
    PRINT 'Index created successfully.'
END
ELSE
BEGIN
    PRINT 'Index already exists.'
END
GO

-- Step 3: Update existing records with user information where possible
PRINT 'Updating existing records with user information...'

-- Update records where we can match by email
UPDATE b
SET b.Booking_Created_By = u.Id
FROM [dbo].[RLT_BOOKING] b
INNER JOIN [Identity].[User] u ON b.Mail_Id = u.Email
WHERE b.Booking_Created_By IS NULL
AND b.Mail_Id IS NOT NULL
AND b.Mail_Id != ''

PRINT 'Existing records updated where possible.'
GO

-- Step 4: Update the stored procedure
PRINT 'Updating usp_Booking_Create_V2 stored procedure...'

ALTER PROCEDURE [dbo].[usp_Booking_Create_V2]   
    @Booking_Id nvarchar(30) NULL,  
    @PickUpCity nvarchar(30) NULL,  
    @DropOffCity nvarchar(30) NULL,  
    @TripType nvarchar(30) NULL,  
    @CarCategory nvarchar(30) NULL,  
    @Duration nvarchar(30) NULL,  
    @Distance decimal NULL,  
    @BasicFare decimal NULL,  
    @DriverCharge decimal,  
    @GST decimal NULL,  
    @Fare decimal NULL,  
    @GSTFare decimal NULL,  
    @CouponCode nvarchar(50) NULL,  
    @CouponDiscount decimal NULL,  
    @PickUpAddress nvarchar(200) NULL,  
    @DropOffAddress nvarchar(200) NULL,  
    @PickUpDate date NULL,  
    @PickUpTime nvarchar(10) NULL,  
    @TravelerName nvarchar(200) NULL,  
    @PhoneNumber nvarchar(30) NULL,  
    @MailId nvarchar(30) NULL,  
    @PaymentMode int NULL,  
    @BookingCreatedBy nvarchar(450) NULL,  -- Increased size to handle GUID
    @RazorpayPaymentID nvarchar(30) NULL,  
    @RazorpayOrderID nvarchar(30) NULL,  
    @RazorpaySignature nvarchar(30) NULL,  
    @RazorpayStatus nvarchar(30) NULL,  
    @PickUpAddressLongLat nvarchar(100) NULL,  
    @PickUpAddressLongitude nvarchar(100) NULL,  
    @CashAmountToPayDriver decimal,  
    @PaymentOption int NULL,  
    @TollCharge decimal NULL,
    @PaymentType nvarchar(10) NULL,
    @PartialPaymentAmount decimal(18,2) NULL,
    @RemainingAmountForDriver decimal(18,2) NULL,
    @result nvarchar(50) NULL output  
WITH RECOMPILE   
AS  
BEGIN  
    SET NOCOUNT ON;  
    SET XACT_ABORT ON;  
    
    DECLARE @errorMessage nvarchar(max),  
           @ScriptRanOn datetime = GETDATE(),  
           @Booking_Date datetime = GETDATE(),  
           @FromCityID int = 0,  
           @TripTypeID int = 0,  
           @CategoryID int = 0,  
           @ToCityID int = 0,
           @UserID int = 0
    
    SET @FromCityID = (SELECT TOP 1 PKID FROM RLT_CITY WHERE CitY_Name = @PickUpCity AND Is_Active = 1);  
    SET @ToCityID = (SELECT TOP 1 PKID FROM RLT_CITY WHERE CitY_Name = @DropOffCity AND Is_Active = 1); 
    SET @TripTypeID = (SELECT TOP 1 PKID FROM RLT_TRIP_TYPES WHERE Trip_Type = @TripType AND Is_Active = 1);  
    SET @CategoryID = (SELECT TOP 1 PKID FROM RLT_CAR_CATEGORY WHERE Car_Category_Abbr = @CarCategory AND Is_Active = 1);  
    
    -- For Created_By (int field), we'll use 1 for valid users, 0 for invalid
    -- The important field is Booking_Created_By which stores the actual user identifier
    IF EXISTS (SELECT 1 FROM [Identity].[User] WHERE Id = @BookingCreatedBy)
        SET @UserID = 1  -- Valid user found by GUID
    ELSE IF EXISTS (SELECT 1 FROM [Identity].[User] WHERE UserName = @BookingCreatedBy)
        SET @UserID = 1  -- Valid user found by username
    ELSE
        SET @UserID = 0  -- No valid user found
    
    -- Set default payment type if not provided
    IF @PaymentType IS NULL OR @PaymentType = ''
        SET @PaymentType = 'FULL'
    
    BEGIN TRANSACTION  
        INSERT INTO [dbo].[RLT_BOOKING](
            Booking_Id,  
            City_From_Id,   
            City_To_Id,  
            Trip_Type_Id,  
            Car_Category_Id,  
            Duration,  
            Distance,  
            Basic_Fare,  
            Driver_Charge,  
            GST,  
            Fare,  
            GST_Fare,  
            Coupon_Code,  
            Coupon_Discount,  
            Booking_Date,  
            PickUp_Address,  
            DropOff_Address,  
            PickUp_Date,  
            PickUp_Time,  
            [Name],  
            Mobile_No1,  
            Mail_Id,  
            Mode_Of_Payment_Id,  
            Booking_Status_Id,
            Created_By,
            Booking_Created_By,  -- Added this field
            razorpay_payment_id,
            razorpay_order_id,
            razorpay_signature,
            razorpay_status,
            PickUpAddressLatitude,
            PickUpAddressLongitude,
            CashAmountToPayDriver,
            PaymentOption,
            TollCharge,
            PaymentType,
            PartialPaymentAmount,
            RemainingAmountForDriver
        ) VALUES (  
            @Booking_Id,  
            @FromCityID,  
            @ToCityID,  
            @TripTypeID,  
            @CategoryID,  
            @Duration,  
            @Distance,  
            @BasicFare,  
            @DriverCharge,  
            @GST,  
            @Fare,  
            @GSTFare,  
            @CouponCode,  
            @CouponDiscount,  
            GETDATE(),  
            @PickUpAddress,  
            @DropOffAddress,  
            @PickUpDate,  
            @PickUpTime,  
            @TravelerName,  
            @PhoneNumber,  
            @MailId,  
            @PaymentMode,  
            1, /*New Booking Request*/
            @UserID,
            @BookingCreatedBy,  -- Added this value
            @RazorpayPaymentID,
            @RazorpayOrderID,
            @RazorpaySignature,
            @RazorpayStatus,
            @PickUpAddressLongLat,
            @PickUpAddressLongitude,
            @CashAmountToPayDriver,
            @PaymentOption,
            @TollCharge,
            @PaymentType,
            @PartialPaymentAmount,
            @RemainingAmountForDriver
        )  
           
        SET @errorMessage = CONVERT(nvarchar(max), GETDATE(), 21) + ' running usp_Booking_Create_V2 => final--committing sql transaction'  
        RAISERROR(@errorMessage, 0, 1) WITH NOWAIT  
    COMMIT TRANSACTION   

    SET @result = SCOPE_IDENTITY()
END
GO

PRINT 'usp_Booking_Create_V2 stored procedure updated successfully!'
PRINT ''
PRINT '=== SCHEMA UPDATE COMPLETED ==='
PRINT 'Changes made:'
PRINT '1. Added Booking_Created_By NVARCHAR(450) column to RLT_BOOKING table'
PRINT '2. Created index IX_RLT_BOOKING_Booking_Created_By for performance'
PRINT '3. Updated existing records where possible (by email match)'
PRINT '4. Updated usp_Booking_Create_V2 to include Booking_Created_By field'
PRINT '5. Increased @BookingCreatedBy parameter size to 450 to handle GUIDs'
PRINT ''
PRINT 'Next steps:'
PRINT '- Test booking creation via PhonePe token endpoint'
PRINT '- Execute the user bookings stored procedure'
PRINT '- Test the user bookings API'
