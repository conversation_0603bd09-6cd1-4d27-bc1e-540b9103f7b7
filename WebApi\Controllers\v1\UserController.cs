﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers.v1
{
    [ApiVersion("1.0")]
    public class UserController : BaseApiController
    {

        // POST api/<controller>
        [HttpPost("UserPhotoUpdate")]
        [Authorize]
        public async Task<IActionResult> Post()
        {
            return Ok();
        }

      
    }
}