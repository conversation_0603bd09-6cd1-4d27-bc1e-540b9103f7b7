﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltCarOwnerDetails
    {
        public int Pkid { get; set; }
        public int? VendorPkid { get; set; }
        public string CarOwnerName { get; set; }
        public string CarOwnerPhoto { get; set; }
        public string CarOwnerPhone1 { get; set; }
        public string CarOwnerPhone2 { get; set; }
        public string Phone1IsWhatsup { get; set; }
        public string CarOwnerEmail { get; set; }
        public string CarOwnerDl { get; set; }
        public string CarOwnerAadhaar { get; set; }
        public string CarOwnerAddress { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdateDate { get; set; }
        public int? CreatedBy { get; set; }
        public int? UpdatedBy { get; set; }
    }
}
