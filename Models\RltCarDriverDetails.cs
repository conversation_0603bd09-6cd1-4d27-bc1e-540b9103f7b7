﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltCarDriverDetails
    {
        public int Pkid { get; set; }
        public Guid? PkGuid { get; set; }
        public int? VendorPkid { get; set; }
        public int? CarOwnerPkid { get; set; }
        public long? CarPkid { get; set; }
        public string DriverName { get; set; }
        public string DriverPhoto { get; set; }
        public DateTime? DriverDob { get; set; }
        public string DriverFname { get; set; }
        public string DriverMname { get; set; }
        public string DriverLname { get; set; }
        public string DrivePhone1 { get; set; }
        public string DrivePhone2 { get; set; }
        public string Phone1IsWhatsup { get; set; }
        public string DriverEmail { get; set; }
        public string DriverDl { get; set; }
        public string DriverAadhaar { get; set; }
        public string DriverFatherName { get; set; }
        public string DriverMotherName { get; set; }
        public string DriverMarritalStatus { get; set; }
        public string DriverGender { get; set; }
        public bool? DriverCurrentPermanentSame { get; set; }
        public string DriverCurrentAddress { get; set; }
        public string DriverPermanentAddress { get; set; }
        public string DriverPermanentAddress1 { get; set; }
        public string DriverPermanentAddress2 { get; set; }
        public int? DriverPermanentAddressState { get; set; }
        public int? DriverPermanentAddressCity { get; set; }
        public string DriverCurrentAddress1 { get; set; }
        public string DriverCurrentAddress2 { get; set; }
        public int? DriverCurrentAddressState { get; set; }
        public int? DriverCurrentAddressCity { get; set; }
        public string DriverSmokingStatus { get; set; }
        public string DriverDrinkingStatus { get; set; }
        public string DriverEatingType { get; set; }
        public string DriverReligion { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public int? UpdatedBy { get; set; }
        public string WillOwnerDriveCarStatus { get; set; }
        public string DriverPwd { get; set; }
        public DateTime? OtpupdatedDateTime { get; set; }
        public int? Otpnumber { get; set; }
        public string UserId { get; set; }
        public string SpecialRemarking { get; set; }
    }
}
