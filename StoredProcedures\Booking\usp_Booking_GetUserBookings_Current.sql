-- =============================================
-- Stored Procedure: usp_Booking_GetUserBookings (Current Database Version)
-- Description: Retrieves paginated user bookings using existing schema
-- Author: CabYaari Development Team
-- Created: 2025-07-06
-- Note: This version works with current database schema (without Booking_Created_By column)
-- =============================================

USE [CabYaari]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- Drop existing procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'usp_Booking_GetUserBookings')
BEGIN
    DROP PROCEDURE [dbo].[usp_Booking_GetUserBookings]
END
GO

CREATE PROCEDURE [dbo].[usp_Booking_GetUserBookings]
    @UserId NVARCHAR(50),
    @Cursor NVARCHAR(50) = NULL,
    @PageSize INT = 10
WITH RECOMPILE
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    -- Validate page size (max 10)
    IF @PageSize > 10
        SET @PageSize = 10;
    
    IF @PageSize <= 0
        SET @PageSize = 10;

    -- Parse cursor date
    DECLARE @CursorDate DATETIME = NULL;
    IF @Cursor IS NOT NULL AND @Cursor != ''
    BEGIN
        BEGIN TRY
            SET @CursorDate = CAST(@Cursor AS DATETIME);
        END TRY
        BEGIN CATCH
            SET @CursorDate = NULL;
        END CATCH
    END;

    -- Get bookings with pagination using JOIN with Identity.User table
    WITH BookingData AS (
        SELECT
            b.Booking_Id as BookingId,
            cf.CitY_Name as PickUpCity,
            ct.CitY_Name as DropOffCity,
            tt.Trip_Type as TripType,
            cc.Car_Category_Abbr as CarCategory,
            ROUND(b.Fare, 0) as Fare,  -- Round fare to no decimal places
            b.PickUp_Address as PickUpAddress,
            b.DropOff_Address as DropOffAddress,
            b.PickUp_Date as PickUpDate,
            b.PickUp_Time as PickUpTime,
            b.[Name] as TravelerName,
            b.Mobile_No1 as PhoneNumber,
            b.razorpay_status as RazorpayStatus,
            b.Created_Date as BookingDate,
            ROW_NUMBER() OVER (ORDER BY b.Created_Date DESC) as RowNum
        FROM [dbo].[RLT_BOOKING] b
        INNER JOIN [dbo].[RLT_CITY] cf ON b.City_From_Id = cf.PKID
        INNER JOIN [dbo].[RLT_CITY] ct ON b.City_To_Id = ct.PKID
        INNER JOIN [dbo].[RLT_TRIP_TYPES] tt ON b.Trip_Type_Id = tt.PKID
        INNER JOIN [dbo].[RLT_CAR_CATEGORY] cc ON b.Car_Category_Id = cc.PKID
        INNER JOIN [Identity].[User] u ON CAST(b.Created_By AS NVARCHAR) = u.Id
        WHERE u.UserName = @UserId
        AND (@CursorDate IS NULL OR b.Created_Date < @CursorDate)
    )
    SELECT TOP (@PageSize + 1) *
    FROM BookingData
    ORDER BY BookingDate DESC;
END
GO

PRINT 'usp_Booking_GetUserBookings created successfully (current database version)!'

-- =============================================
-- Usage Examples:
-- 
-- Get first page:
-- EXEC [dbo].[usp_Booking_GetUserBookings] @UserId = 'testuser', @PageSize = 10
-- 
-- Get next page with cursor:
-- EXEC [dbo].[usp_Booking_GetUserBookings] @UserId = 'testuser', @Cursor = '2025-07-06T10:30:00.000Z', @PageSize = 10
-- =============================================
