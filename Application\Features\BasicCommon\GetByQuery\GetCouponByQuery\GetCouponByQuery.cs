﻿using Application.DTOs.BasicCommon;
using Application.Exceptions;
using Application.Interfaces.Repositories;
using Application.Wrappers;
using MediatR;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Application.Features.BasicCommon.GetByQuery.GetCouponByQuery
{
   public class GetCouponByQuery : IRequest<Response<DiscountCouponResponse>>
    {
        public string CouponCode { get; set; }
 

        public class GetCouponByQueryHandler : IRequestHandler<GetCouponByQuery, Response<DiscountCouponResponse>>
        {
            private readonly ICommonRepositoryAsync _commonRepositoryAsync;
            public GetCouponByQueryHandler(ICommonRepositoryAsync commonRepositoryAsync)
            {
                _commonRepositoryAsync = commonRepositoryAsync;
            }
            public async Task<Response<DiscountCouponResponse>> Handle(GetCouponByQuery query, CancellationToken cancellationToken)
            {

                var city = await _commonRepositoryAsync.VerifyDiscountCoupon(query.CouponCode);
                if (city == null) throw new ApiException($"Coupon code not applicable.");
                return new Response<DiscountCouponResponse>(city);
            }
        }
    }
}
