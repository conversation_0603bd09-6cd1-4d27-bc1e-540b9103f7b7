﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Application.Features.Bookings.Common
{
  public static class BookingUtility
    {

        public static string GenerateBookingID()
        {
            Random generator = new Random();
            String r = "CY-" + DateTime.Now.ToString("dd") + DateTime.Now.ToString("MM") + DateTime.Now.ToString("yy") + "-" + generator.Next(0, 99999).ToString("D5");
            return r;
        }
    }
}
