# Payment Type Implementation Summary

## Overview
This document summarizes the changes made to support partial and full payment types in the PhonePe payment processing system.

## Changes Made

### 1. BookingCommand Updates
**File:** `Application/Features/Bookings/CreateBooking/BookingCommand.cs`
- Added `PaymentType` field (string) - "PARTIAL" or "FULL"
- Added `PartialPaymentAmount` field (decimal?) - Amount to be paid online for partial payments
- Added `RemainingAmountForDriver` field (decimal?) - Amount to be collected by driver

### 2. RLT_BOOKING Entity Updates
**File:** `Domain/Entities/Rlt_Booking.cs`
- Added same three fields as BookingCommand to maintain consistency

### 3. PhonePe Payment DTOs Updates
**File:** `Application/DTOs/Payment/PhonePePaymentDto.cs`
- Updated `PhonePeVerifyData` class to include:
  - `PaymentType` field
  - `PartialPaymentAmount` field
  - `RemainingAmountForDriver` field
- Added `PaymentType` constants class with PARTIAL and FULL values

### 4. PhonePe Token Generation Updates
**File:** `Application/Features/Payments/PhonePe/GeneratePhonePeToken.cs`
- Enhanced payment amount calculation logic:
  - For PARTIAL payments: Uses `PartialPaymentAmount` as payment amount
  - For FULL payments: Uses total `Fare` as payment amount
  - Automatically calculates `RemainingAmountForDriver` for partial payments
- Updated amount validation to use calculated payment amount instead of total fare

### 5. PhonePe Payment Verification Updates
**File:** `Application/Features/Payments/PhonePe/VerifyPhonePePayment.cs`
- Enhanced verification response to include:
  - Payment type information
  - Partial payment amount
  - Remaining amount for driver

### 6. Database Repository Updates
**File:** `Infrastructure.Persistence/Repositories/BookingRepositoryAsync.cs`
- Updated `AddNewAsync` method to include new payment fields in stored procedure call
- Updated `UpdateAsync` method to include new payment fields in stored procedure call
- Changed stored procedure names to `usp_Booking_Create_V2` and `usp_Booking_Update_V2`

## API Usage Examples

### Token Generation Request (Partial Payment)
```json
{
  "tripType": "One Way",
  "duration": "2 hrs 33 mins",
  "distance": 159,
  "basicFare": 4000,
  "gst": 200,
  "fare": 800,
  "paymentType": "PARTIAL",
  "partialPaymentAmount": 800,
  "cashAmountToPayDriver": 3200,
  "travelerName": "Kunal Saini",
  "phoneNumber": "8824516274",
  "mailId": "<EMAIL>",
  "paymentMode": 0,
  "paymentOption": 1,
  "pickUpCity": "Jaipur",
  "dropOffCity": "Pushkar",
  "carCategory": "SUV Premium",
  "pickUpAddress": "Jaipur International Airport...",
  "dropOffAddress": "A Blue Star Garden Restaurant...",
  "callbackUrl": "http://localhost:4200/payment-callback"
}
```

### Token Generation Request (Full Payment)
```json
{
  "tripType": "One Way",
  "duration": "2 hrs 33 mins",
  "distance": 159,
  "basicFare": 4000,
  "gst": 200,
  "fare": 4200,
  "paymentType": "FULL",
  "cashAmountToPayDriver": 0,
  "travelerName": "Kunal Saini",
  "phoneNumber": "8824516274",
  "mailId": "<EMAIL>",
  "paymentMode": 0,
  "paymentOption": 1,
  "pickUpCity": "Jaipur",
  "dropOffCity": "Pushkar",
  "carCategory": "SUV Premium",
  "pickUpAddress": "Jaipur International Airport...",
  "dropOffAddress": "A Blue Star Garden Restaurant...",
  "callbackUrl": "http://localhost:4200/payment-callback"
}
```

### Payment Verification Response
```json
{
  "succeeded": true,
  "message": "Payment verification completed",
  "errors": null,
  "data": {
    "paymentStatus": "COMPLETED",
    "transactionId": "OM2507051621194399258625",
    "orderId": "OMO2507051621194389258370",
    "bookingId": "BK123456789",
    "amount": 800.00,
    "paymentId": "OM2507051621194399258625",
    "paymentType": "PARTIAL",
    "partialPaymentAmount": 800.00,
    "remainingAmountForDriver": 3200.00
  }
}
```

## Required Database Changes

### New Stored Procedures Needed
1. **usp_Booking_Create_V2** - Enhanced version of usp_Booking_Create with new parameters:
   - @PaymentType VARCHAR(10)
   - @PartialPaymentAmount DECIMAL(18,2)
   - @RemainingAmountForDriver DECIMAL(18,2)

2. **usp_Booking_Update_V2** - Enhanced version of usp_Booking_Update with new parameters:
   - @PaymentType VARCHAR(10)
   - @PartialPaymentAmount DECIMAL(18,2)
   - @RemainingAmountForDriver DECIMAL(18,2)

### Database Schema Changes
The following columns need to be added to the booking tables:
- PaymentType VARCHAR(10)
- PartialPaymentAmount DECIMAL(18,2)
- RemainingAmountForDriver DECIMAL(18,2)

## Next Steps Required

1. **Create Database Schema Changes**
   - Add new columns to RLT_BOOKING table
   - Add new columns to RLT_BOOKING_Histrory table
   - Create new stored procedures usp_Booking_Create_V2 and usp_Booking_Update_V2

2. **Testing**
   - Test partial payment flow
   - Test full payment flow
   - Verify payment verification includes new fields
   - Test edge cases (invalid payment amounts, etc.)

3. **Documentation**
   - Update API documentation to include new payment type parameters
   - Update integration guides for frontend applications

## Implementation Notes

- The system defaults to "FULL" payment type if not specified
- For partial payments, the system automatically calculates remaining amount for driver
- Payment amount validation ensures minimum payment requirements are met
- All existing functionality remains backward compatible
