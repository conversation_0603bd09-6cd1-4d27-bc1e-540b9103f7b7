﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltCarOwnerBankDetails
    {
        public int Pkid { get; set; }
        public Guid? PkGuid { get; set; }
        public int? CarOwnerPkid { get; set; }
        public int? BankNamePkid { get; set; }
        public string BankIfscCode { get; set; }
        public string CarOwnerBankAccName { get; set; }
        public string CarOwnerBankAccNumber { get; set; }
        public string BankAccBranchAddress { get; set; }
        public string CarOwnerBankRegisteredPhone { get; set; }
        public string CarOwnerBankCancelledChkPhoto { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatdeDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public int? UpdatedBy { get; set; }
        public string UpdaterComment { get; set; }

        public virtual RltBankNames BankNamePk { get; set; }
    }
}
