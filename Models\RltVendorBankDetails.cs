﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltVendorBankDetails
    {
        public int Pkid { get; set; }
        public int? BankNamePkid { get; set; }
        public string IfscCode { get; set; }
        public string AccountHolderName { get; set; }
        public string AccountNumber { get; set; }
        public int VendorRefPkid { get; set; }
        public bool? BankStatus { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public int LastmodifiedBy { get; set; }
        public bool IsActive { get; set; }
    }
}
