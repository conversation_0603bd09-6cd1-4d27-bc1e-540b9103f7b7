﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltDiscountCoupon
    {
        public int Pkid { get; set; }
        public string DiscountCoupon { get; set; }
        public decimal? Discount { get; set; }
        public string CouponName { get; set; }
        public string CouponRemark { get; set; }
        public int? MaxDiscount { get; set; }
        public int? FareWhenApplied { get; set; }
        public DateTime? CouponLastDate { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public int? UpdatedBy { get; set; }
    }
}
