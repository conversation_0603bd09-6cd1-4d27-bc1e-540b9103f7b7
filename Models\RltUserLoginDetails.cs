﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltUserLoginDetails
    {
        public RltUserLoginDetails()
        {
            RltBookingPaymentDetails = new HashSet<RltBookingPaymentDetails>();
        }

        public long Pkid { get; set; }
        public Guid? PkGuid { get; set; }
        public string RltUserName { get; set; }
        public string RltUserPhone { get; set; }
        public string RltUserEmail { get; set; }
        public string RltUserLoginSignupMethod { get; set; }
        public string RltUserAggreed { get; set; }
        public string RltUserPwd { get; set; }
        public string RltUserGender { get; set; }
        public DateTime? RltUserDob { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public DateTime? LastLoginDateTime { get; set; }
        public bool? RltUser2faAuthStatus { get; set; }
        public int? Otpnumber { get; set; }
        public DateTime? OtpupdatedDateTime { get; set; }
        public string UserId { get; set; }

        public virtual ICollection<RltBookingPaymentDetails> RltBookingPaymentDetails { get; set; }
    }
}
