﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltCarDriverDocs
    {
        public int Pkid { get; set; }
        public int CarDriverPkid { get; set; }
        public int CarDriverDocId { get; set; }
        public DateTime? CarDriverDocEndDate { get; set; }
        public string DocumentPath { get; set; }
        public bool IsVerified { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public int LastModifiedBy { get; set; }
    }
}
