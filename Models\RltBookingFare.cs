﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltBookingFare
    {
        public int Pkid { get; set; }
        public int? CityFrom { get; set; }
        public int? CityTo { get; set; }
        public int? TripTypeId { get; set; }
        public int? CarCategoryId { get; set; }
        public decimal? BasicFare { get; set; }
        public decimal? DriverCharge { get; set; }
        public decimal? TollCharge { get; set; }
        public decimal? TotalFare { get; set; }
        public decimal? Gst { get; set; }
        public decimal? GstAmount { get; set; }
        public decimal? FinalFare { get; set; }
        public string Remark { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public int? UpdatedBy { get; set; }
    }
}
