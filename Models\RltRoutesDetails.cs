﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltRoutesDetails
    {
        public long Pkid { get; set; }
        public Guid? PkGuid { get; set; }
        public int? RouteDistanceKm { get; set; }
        public int? TotalPaidTollsInRoute { get; set; }
        public int? PickupCityPkid { get; set; }
        public int? DropoffCityPkid { get; set; }
        public int? CarCategoryPkid { get; set; }
        public int? CarTripTypePkid { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }

        public virtual RltCarCategory CarCategoryPk { get; set; }
        public virtual RltTripTypes CarTripTypePk { get; set; }
        public virtual RltCity DropoffCityPk { get; set; }
        public virtual RltCity PickupCityPk { get; set; }
    }
}
