﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class TempBooking
    {
        public int Id { get; set; }
        public string UserName { get; set; }
        public string MobileNumber { get; set; }
        public string PickUpAddress { get; set; }
        public string DropOffAddress { get; set; }
        public DateTime? TravelDate { get; set; }
        public string TravelTime { get; set; }
        public bool IsActive { get; set; }
        public DateTime? BookingDate { get; set; }
    }
}
