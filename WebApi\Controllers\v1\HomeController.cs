﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Application.Features.BasicCommon.GetAll.GetMostFavourite;
using Application.Features.BasicCommon.GetByQuery.GetCouponByQuery;
using Application.Features.BasicCommon.GetByQuery.GetLongLatByCityNameQuery;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers.v1
{
    [ApiVersion("1.0")]
    public class HomeController : BaseApiController
    {

        // POST api/<controller>
        [HttpGet("GetCityInfo")]
        // [Authorize]
        public async Task<IActionResult> Get([FromQuery] string pickUpCity, [FromQuery] string dropOffCity)
        {
            return Ok(await Mediator.Send(new GetCityByNameQuery { PickUpCityName = pickUpCity, DropOffCityName = dropOffCity}));
        }

        // POST api/<controller>
        [HttpGet("MostFavouriteRoutes")]
        // [Authorize]
        public async Task<IActionResult> Get()
        {
            return Ok(await Mediator.Send(new GetMostFavouriteQuery {}));
        }


        [HttpGet("Discount")]
        // [Authorize]
        public async Task<IActionResult> Get([FromQuery] string couponCode)
        {
            return Ok(await Mediator.Send(new GetCouponByQuery { CouponCode = couponCode }));
        }


    }
}