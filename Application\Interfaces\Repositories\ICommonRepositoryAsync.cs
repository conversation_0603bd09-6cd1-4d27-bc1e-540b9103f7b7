﻿using Application.DTOs.BasicCommon;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Application.Interfaces.Repositories
{
    public interface ICommonRepositoryAsync 
    {
        public Task<List<CityResponse>> GetCityByCityNames(string pickCityName,string dropOffCityName);

        public Task<DiscountCouponResponse> VerifyDiscountCoupon(string couponCode);

        public Task<List<MostFavouriteRoutesResponse>> GetMostFavouriteRoutes();
    }
}
