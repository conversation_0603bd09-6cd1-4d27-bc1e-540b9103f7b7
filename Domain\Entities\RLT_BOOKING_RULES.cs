﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Domain.Entities
{
  public class RLT_BOOKING_RULES :AuditableProperty
    {
        [Key]
        public int PKID { get; set; }
        public int Trip_Type_Id { get; set; }
        public int Car_Category_Id { get; set; }
        public int Distance_From { get; set; }
        public int Distance_To { get; set; }
        public int NewDistance { get; set; }

    }
}
