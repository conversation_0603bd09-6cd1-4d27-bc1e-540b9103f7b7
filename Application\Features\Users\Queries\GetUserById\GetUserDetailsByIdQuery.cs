﻿using Application.Exceptions;
using Application.Interfaces.Repositories;
using Application.Wrappers;
using Domain.Entities;
using MediatR;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Application.Features.Users.Queries.GetUserById
{
  public  class GetUserDetailsByIdQuery : IRequest<Response<User>>
    {
        public string UserId{ get; set; }
        public class GetUserDetailsByIdQueryHandler : IRequestHandler<GetUserDetailsByIdQuery, Response<User>>
        {
            private readonly IApplicationUserAsync _appUserRepositoryAsync;
            public GetUserDetailsByIdQueryHandler(IApplicationUserAsync appUserRepositoryAsync)
            {
                _appUserRepositoryAsync = appUserRepositoryAsync;
            }
            public async Task<Response<User>> Handle(GetUserDetailsByIdQuery query, CancellationToken cancellationToken)
            {

                var user = await _appUserRepositoryAsync.GetByUniqueIdAsync(query.UserId);
                if (user == null) throw new ApiException($"User Not Found.");
                return new Response<User>(user);
            }
        }

    }
}
