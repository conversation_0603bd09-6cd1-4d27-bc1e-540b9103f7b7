using System;

namespace Application.DTOs.Booking
{
    /// <summary>
    /// Secure response DTO for booking details that excludes sensitive information
    /// </summary>
    public class BookingDetailsResponse
    {
        public string BookingID { get; set; }
        public string PickUpCity { get; set; }
        public string DropOffCity { get; set; }
        public string TripType { get; set; }
        public string CarCategory { get; set; }
        public string Duration { get; set; }
        public decimal? Distance { get; set; }
        public decimal? BasicFare { get; set; }
        public decimal? DriverCharge { get; set; }
        public decimal? Gst { get; set; }
        public decimal? Fare { get; set; }
        public decimal? GstFare { get; set; }
        public string CouponCode { get; set; }
        public decimal? CouponDiscount { get; set; }
        public DateTime? BookingDate { get; set; }
        public string PickUpAddress { get; set; }
        public string DropOffAddress { get; set; }
        public DateTime? PickUpDate { get; set; }
        public string PickUpTime { get; set; }
        public string TravelerName { get; set; }
        public int? PaymentMode { get; set; }
        public int? BookingStatusId { get; set; }
        public string PaymentStatus { get; set; } // User-friendly payment status
        public decimal CashAmountToPayDriver { get; set; }
        public int PaymentOption { get; set; }
        public decimal? TollCharge { get; set; }
        
        // Partial payment fields
        public string PaymentType { get; set; }
        public decimal? PartialPaymentAmount { get; set; }
        public decimal? RemainingAmountForDriver { get; set; }
        
        // ❌ EXCLUDED sensitive fields for security:
        // - PhoneNumber (personal data)
        // - MailId (personal data)
        // - RazorpayPaymentId (payment gateway details)
        // - RazorpayOrderid (payment gateway details)
        // - RazorpaySignature (security sensitive)
        // - RazorpayStatus (internal status)
        // - BookingCreatedBy (internal user identifier)
        // - PickUpAddressLongLat (exact coordinates)
        // - DropOffAddressLongLat (exact coordinates)
        // - PKID (internal database ID)
        // - Created_By (internal user ID)
    }
}
