﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Application.Interfaces
{
    public interface IMapService
    {
        Task<string> GenerateMapToken();
        Task MapTextSearch(string placeName,string token);
        Task MapCitySearch(string placeName, string token);
        Task MapAutoSuggest(string placeName,string token, string eloc);
        Task MapNearBy(string refLocation, string userName, string keyword, string token);
        Task MapEtaCalculation(string fromLatLong, string toLatLong);
        Task MapDistanceMatrix(string fromLatLang,string toLatLang);
    }
}
