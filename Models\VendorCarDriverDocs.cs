﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class VendorCarDriverDocs
    {
        public int Pkid { get; set; }
        public int DocName { get; set; }
        public int VendorCarRef { get; set; }
        public string DocumentPath { get; set; }
        public bool IsVerified { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public int LastModifiedBy { get; set; }
        public string Comments { get; set; }

        public virtual RltDocumnetsName DocNameNavigation { get; set; }
        public virtual RltVendorCarDetails VendorCarRefNavigation { get; set; }
    }
}
