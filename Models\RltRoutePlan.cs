﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltRoutePlan
    {
        public int Pkid { get; set; }
        public int? FromCityPkid { get; set; }
        public int? ToCityPkid { get; set; }
        public int? CarPkid { get; set; }
        public int? FecilitiesPkid { get; set; }
        public int? CarClassPkid { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public int? ChargesMasterPkid { get; set; }
    }
}
