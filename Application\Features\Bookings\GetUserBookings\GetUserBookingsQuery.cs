using Application.DTOs.Booking;
using Application.Interfaces.Repositories;
using Application.Wrappers;
using MediatR;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Application.Features.Bookings.GetUserBookings
{
    public class GetUserBookingsQuery : IRequest<Response<UserBookingsResponse>>
    {
        public string UserId { get; set; }
        public string Cursor { get; set; }
        public int PageSize { get; set; } = 10;
    }

    public class GetUserBookingsQueryHandler : IRequestHandler<GetUserBookingsQuery, Response<UserBookingsResponse>>
    {
        private readonly IBookingRepositoryAsync _bookingRepositoryAsync;

        public GetUserBookingsQueryHandler(IBookingRepositoryAsync bookingRepositoryAsync)
        {
            _bookingRepositoryAsync = bookingRepositoryAsync;
        }

        public async Task<Response<UserBookingsResponse>> Handle(GetUserBookingsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var result = await _bookingRepositoryAsync.GetUserBookingsByUserIdAsync(
                    request.UserId, 
                    request.Cursor, 
                    request.PageSize);

                if (result == null)
                {
                    return new Response<UserBookingsResponse>("No bookings found for the user.");
                }

                return new Response<UserBookingsResponse>(result, "User bookings retrieved successfully.");
            }
            catch (Exception ex)
            {
                return new Response<UserBookingsResponse>($"Error retrieving user bookings: {ex.Message}");
            }
        }
    }
}
