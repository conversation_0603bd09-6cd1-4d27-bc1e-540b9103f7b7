﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltDocumnetsName
    {
        public RltDocumnetsName()
        {
            RltVendorDocs = new HashSet<RltVendorDocs>();
            VendorCarDriverDocs = new HashSet<VendorCarDriverDocs>();
        }

        public int Pkid { get; set; }
        public string DocFor { get; set; }
        public string DocName { get; set; }
        public bool? IsDocReq { get; set; }
        public bool? IsDocExpiryDate { get; set; }
        public bool IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }

        public virtual ICollection<RltVendorDocs> RltVendorDocs { get; set; }
        public virtual ICollection<VendorCarDriverDocs> VendorCarDriverDocs { get; set; }
    }
}
