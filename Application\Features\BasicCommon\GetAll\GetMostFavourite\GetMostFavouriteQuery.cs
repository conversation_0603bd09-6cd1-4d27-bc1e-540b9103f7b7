﻿using Application.DTOs.BasicCommon;
using Application.Exceptions;
using Application.Interfaces.Repositories;
using Application.Wrappers;
using AutoMapper;
using MediatR;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Application.Features.BasicCommon.GetAll.GetMostFavourite
{
    public class GetMostFavouriteQuery : IRequest<Response<List<MostFavouriteRoutesResponse>>>
    {
        public class GetMostFavouriteQueryHandler : IRequestHandler<GetMostFavouriteQuery, Response<List<MostFavouriteRoutesResponse>>>
        {
            private readonly ICommonRepositoryAsync _commonRepositoryAsync;
            public GetMostFavouriteQueryHandler(ICommonRepositoryAsync commonRepositoryAsync)
            {
                _commonRepositoryAsync = commonRepositoryAsync;
            }
            public async Task<Response<List<MostFavouriteRoutesResponse>>> Handle(GetMostFavouriteQuery query, CancellationToken cancellationToken)
            {

                var mostFavouriteRoutes = await _commonRepositoryAsync.GetMostFavouriteRoutes();
                if (mostFavouriteRoutes == null) throw new ApiException($"Most Favourite Routes Not Found.");
                return new Response<List<MostFavouriteRoutesResponse>>(mostFavouriteRoutes);
            }
        }

    }


}
