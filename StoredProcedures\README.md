# Stored Procedures Directory

This directory contains all stored procedures used by the CabYaari Web API.

## Organization

- **Booking/**: Stored procedures related to booking operations
- **User/**: Stored procedures related to user operations
- **Payment/**: Stored procedures related to payment operations

## Naming Convention

- Use prefix `usp_` for all stored procedures
- Use descriptive names that indicate the operation
- Example: `usp_Booking_GetUserBookings`, `usp_Payment_UpdateStatus`

## Usage

Each stored procedure file includes:
1. Purpose and description
2. Parameters documentation
3. Return value documentation
4. Usage examples
5. Version history

## Deployment

Execute these stored procedures in the target database before deploying the application.
