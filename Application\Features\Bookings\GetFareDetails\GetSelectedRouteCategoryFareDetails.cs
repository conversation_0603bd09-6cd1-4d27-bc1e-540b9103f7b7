﻿using Application.DTOs.BasicCommon;
using Application.DTOs.Booking;
using Application.Interfaces.Repositories;
using Application.Wrappers;
using Dapper;
using Domain.Settings;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Application.Features.Bookings.GetFareDetails
{
    public class GetSelectedRouteCategoryFareDetails : IRequest<Response<SelectedRoutesCategoryResponse>>
    {
        public string PickUpAddressLongLat { get; set; }

        public string DropOffAddressLongLat { get; set; }

        public string TripType { get; set; }

        public string CarCagetgory { get; set; }



        public class GetSelectedRouteCategoryFareDetailsHandler : IRequestHandler<GetSelectedRouteCategoryFareDetails, Response<SelectedRoutesCategoryResponse>>
        {
            private readonly IBookingRepositoryAsync _bookingRepositoryAsync;
            private readonly IConfiguration _configuration;
            public MapSettings _mapSettings { get; }
            public GetSelectedRouteCategoryFareDetailsHandler(IBookingRepositoryAsync bookingRepositoryAsync, IConfiguration configuration, IOptions<MapSettings> mapSettings)
            {
                _bookingRepositoryAsync = bookingRepositoryAsync;
                _configuration = configuration;
                _mapSettings = mapSettings.Value;

            }

            /// <summary>
            /// Calculates compensated distance for time-based fare compensation
            /// If average speed < 66 km/h, compensate drivers by calculating fare based on 60 km/h standard
            /// </summary>
            /// <param name="actualDistanceKM">Actual distance from API in kilometers</param>
            /// <param name="journeyTimeSeconds">Journey time from API in seconds</param>
            /// <returns>Distance to use for billing (actual or compensated, whichever is higher)</returns>
            private int CalculateCompensatedDistance(int actualDistanceKM, int journeyTimeSeconds)
            {
                // Add 5-minute buffer to journey time as per business requirement
                int bufferedTimeSeconds = journeyTimeSeconds + (5 * 60); // Add 5 minutes
                double bufferedTimeHours = bufferedTimeSeconds / 3600.0;

                // Calculate average speed: Distance / Time
                double averageSpeed = actualDistanceKM / bufferedTimeHours;

                // Time-based compensation logic:
                // If average speed < 66 km/h, calculate compensated distance using 60 km/h standard
                if (averageSpeed < 66)
                {
                    // Compensated distance = 60 km/h × buffered journey time
                    int compensatedDistance = (int)Math.Round(60 * bufferedTimeHours);

                    // Use the higher value between actual and compensated distance
                    return Math.Max(actualDistanceKM, compensatedDistance);
                }
                else
                {
                    return actualDistanceKM;
                }
            }

            public async Task<Response<SelectedRoutesCategoryResponse>> Handle(GetSelectedRouteCategoryFareDetails query, CancellationToken cancellationToken)
            {
                string perKmCharges = string.Empty;
                string bookingRuleFare = string.Empty;
                string carCategoryId = string.Empty;
                int tripTypeId = 0;

                CarCategory carCategory = new CarCategory();

                BookingFareRules bookingFareRules = new BookingFareRules();
                SelectedRoutesCategoryResponse responsesSelectedRoute = new SelectedRoutesCategoryResponse();

                int Per_KM_fare = 0;
                decimal Basic_Fare = 0, GST_Fare;
                string fixRateNote = "";

                string address = query.PickUpAddressLongLat + ";" + query.DropOffAddressLongLat;

                HttpWebRequest req = null;
                HttpWebResponse res = null;
                // string url = _mapSettings.DistnaceMatrixAPI + _mapSettings.RestAPIKey + " /distance_matrix/driving/" + address + "";
                string url = "https://apis.mapmyindia.com/advancedmaps/v1/" + _mapSettings.RestAPIKey + "/distance_matrix/driving/" + address + "";

                req = (HttpWebRequest)WebRequest.Create(url);
                req.Method = "GET";
                req.ContentType = "application/json; charset=utf-8";
                ASCIIEncoding encoder = new ASCIIEncoding();
                res = (HttpWebResponse)req.GetResponse();
                Stream responseStream = res.GetResponseStream();
                var streamReader = new StreamReader(responseStream);
                string responseString = streamReader.ReadToEnd();
                int distances = 0;
                int actualDistanceKM = 0;
                int journeyTimeSeconds = 0;
                string duration = "";
                if (responseString != "")
                {
                    var jsonObj = JObject.Parse(responseString);
                    JArray distancesArr = (JArray)jsonObj.SelectToken("results.distances[0]");
                    actualDistanceKM = Convert.ToInt32(distancesArr[1]) / 1000;
                    JArray durationsArr = (JArray)jsonObj.SelectToken("results.durations[0]");
                    journeyTimeSeconds = Convert.ToInt32(durationsArr[1]);
                    int hours = journeyTimeSeconds / 3600;
                    int mins = (journeyTimeSeconds % 3600) / 60;
                    duration = hours + " hrs " + mins + " mins ";
                }

                // Apply time-based fare compensation logic
                distances = CalculateCompensatedDistance(actualDistanceKM, journeyTimeSeconds);

                var tripTypeSQL = "SELECT TOP 1 PKID FROM RLT_TRIP_TYPES WHERE Trip_Type ='" + query.TripType + "'";
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    connection.Open();
                    var tripType = await connection.QuerySingleOrDefaultAsync<int>(tripTypeSQL);
                    tripTypeId = tripType;
                    connection.Close();
                }

                var perKmChargesSQL = "SELECT PKID,Car_Category_Abbr as CategoryName,Features,Capacity,Car_Categroy_Image as CategoryImage ,Per_KM_fare as PerKMCharges, Base_Fare as BaseFare FROM RLT_CAR_CATEGORY WHERE Is_Active =1 and Car_Category_Abbr='" + query.CarCagetgory + "'";
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    connection.Open();
                    var response = await connection.QuerySingleOrDefaultAsync<CarCategory>(perKmChargesSQL);


                    carCategory.PKID = response.PKID;
                    carCategory.CategoryName = response.CategoryName;
                    carCategory.CategoryImage = response.CategoryImage;

                    carCategory.PerKMCharges = response.PerKMCharges;
                    carCategory.BaseFare = response.BaseFare;
                    carCategory.Capacity = response.Capacity;
                    carCategory.Features = response.Features;


                    connection.Close();
                }

                var bookingRuleSQL = "SELECT PKID,Car_Category_Id as CarCategoryId,Trip_Type_Id as TripType, NewDistance as FixedFare FROM RLT_BOOKING_RULES WHERE Trip_Type_Id=" + tripTypeId + " and Car_Category_Id =" + carCategory.PKID + " and Distance_From <= " + distances + " and Distance_To >= " + distances + "";
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    connection.Open();
                    var RulesFare = await connection.QuerySingleOrDefaultAsync(bookingRuleSQL);
                    if (RulesFare != null)
                    {
                        bookingFareRules.PKID = RulesFare.PKID;
                        bookingFareRules.FixedFare = RulesFare.FixedFare;
                        bookingFareRules.TripType = RulesFare.TripType;
                        bookingFareRules.CarCategoryId = RulesFare.TripType;
                    }
                    connection.Close();

                }


                if (bookingFareRules.FixedFare >0)
                {

                    decimal fixedFare = bookingFareRules.FixedFare;
                    decimal gstAmount = (fixedFare * 5) / 100;
                    decimal finalFare = fixedFare + gstAmount;

                    responsesSelectedRoute.Distance = distances.ToString("#.##");
                    responsesSelectedRoute.BasicFare = Math.Round(fixedFare, 0).ToString();
                    responsesSelectedRoute.GSTFare = Math.Round(gstAmount, 0).ToString();
                    responsesSelectedRoute.Fare = Math.Round(finalFare, 0).ToString();
                    responsesSelectedRoute.Duration = duration;
                    responsesSelectedRoute.FixRateNote = "1 KM to 125 KM we charge fix "+ responsesSelectedRoute.Fare + " rs";
                    responsesSelectedRoute.Capacity = carCategory.Capacity;
                    responsesSelectedRoute.CategoryName = carCategory.CategoryName;
                    responsesSelectedRoute.CategoryImage = carCategory.CategoryImage;
                    responsesSelectedRoute.PerKMCharges = carCategory.PerKMCharges;
                    responsesSelectedRoute.Capacity = carCategory.Capacity;
                    responsesSelectedRoute.Features = carCategory.Features;

                }
                else
                {
                    // NEW PRICING LOGIC: MAX(Base Fare, Distance Fare) + GST
                    decimal baseFare = carCategory.BaseFare;
                    decimal originalPerKmRate = Convert.ToDecimal(carCategory.PerKMCharges);

                    // Apply distance-based rate adjustment: +1 per KM if distance < 200 KM
                    decimal adjustedPerKmRate = originalPerKmRate;
                    if (distances < 200)
                    {
                        adjustedPerKmRate = originalPerKmRate + 1;
                    }

                    // Calculate distance fare
                    decimal distanceFare = adjustedPerKmRate * distances;

                    // NEW LOGIC: Take maximum of base fare or distance fare
                    decimal basicFare = Math.Max(baseFare, distanceFare);
                    decimal gstAmount = (basicFare * 5) / 100;
                    decimal finalFare = basicFare + gstAmount;

                    // Build comprehensive rate note with new pricing logic
                    string fareType = basicFare == baseFare ? "Base Fare" : "Distance Fare";
                    string rateNote = $"{fareType}: ₹{basicFare.ToString("#.##")} [MAX(Base: ₹{baseFare}, Distance: ₹{distanceFare.ToString("#.##")})]";

                    // Add distance calculation details
                    rateNote += $" (Distance: {distances} KM × ₹{adjustedPerKmRate}/KM)";

                    // Add distance-based rate adjustment note
                    if (distances < 200)
                    {
                        rateNote += " [+₹1/KM for trips < 200 KM]";
                    }

                    // Add time compensation note if billing distance differs from actual distance
                    if (distances != actualDistanceKM)
                    {
                        rateNote += $" [Time compensation: billing {distances} KM vs actual {actualDistanceKM} KM]";
                    }

                    responsesSelectedRoute.Distance = distances.ToString("#.##");
                    responsesSelectedRoute.BasicFare = Math.Round(basicFare, 0).ToString();
                    responsesSelectedRoute.GSTFare = Math.Round(gstAmount, 0).ToString();
                    responsesSelectedRoute.Fare = Math.Round(finalFare, 0).ToString();
                    responsesSelectedRoute.Duration = duration;
                    responsesSelectedRoute.FixRateNote = rateNote;
                    responsesSelectedRoute.Capacity = carCategory.Capacity;
                    responsesSelectedRoute.CategoryName = carCategory.CategoryName;
                    responsesSelectedRoute.CategoryImage = carCategory.CategoryImage;
                    responsesSelectedRoute.PerKMCharges = carCategory.PerKMCharges;
                    responsesSelectedRoute.Features = carCategory.Features;

                }
            

                return new Response<SelectedRoutesCategoryResponse>(responsesSelectedRoute);

            }
    }

}
}
