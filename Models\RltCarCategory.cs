﻿using System;
using System.Collections.Generic;

namespace Infrastructure.Persistence.Models
{
    public partial class RltCarCategory
    {
        public RltCarCategory()
        {
            RltCarChargesFecilitiesDetails = new HashSet<RltCarChargesFecilitiesDetails>();
            RltCarModel = new HashSet<RltCarModel>();
            RltRoutesDetails = new HashSet<RltRoutesDetails>();
            RltVendorCarDetails = new HashSet<RltVendorCarDetails>();
        }

        public int Pkid { get; set; }
        public Guid? PkGuid { get; set; }
        public string CarCategoryName { get; set; }
        public string CarCategoryAbbr { get; set; }
        public double? PerKmFare { get; set; }
        public int? Capacity { get; set; }
        public string Features { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string CarCategroyImage { get; set; }

        public virtual ICollection<RltCarChargesFecilitiesDetails> RltCarChargesFecilitiesDetails { get; set; }
        public virtual ICollection<RltCarModel> RltCarModel { get; set; }
        public virtual ICollection<RltRoutesDetails> RltRoutesDetails { get; set; }
        public virtual ICollection<RltVendorCarDetails> RltVendorCarDetails { get; set; }
    }
}
