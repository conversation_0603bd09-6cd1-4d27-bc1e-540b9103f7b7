﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="Features\BasicCommon\Create\" />
    <Folder Include="Features\Bookings\Repository\" />
    <Folder Include="Features\Drivers\Commands\" />
    <Folder Include="Features\Drivers\Queries\GetDriverById\" />
    <Folder Include="Features\Invoicing\Commands\CreateInvoice\" />
    <Folder Include="Features\Invoicing\Queries\GetInvoiceById\" />
    <Folder Include="Features\Users\Commands\" />
    <Folder Include="Features\Vendors\Commands\" />
    <Folder Include="Features\Vendors\Queries\GetVendorById\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="10.0.0" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Dapper" Version="2.0.35" />
    <PackageReference Include="FluentValidation" Version="9.2.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="9.2.0" />
    <PackageReference Include="MediatR" Version="8.1.0" />
    <PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="8.1.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="3.1.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="3.1.7" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="3.1.7" />
    <PackageReference Include="Newtonsoft.Json" Version="12.0.3" />
    <PackageReference Include="RazorPayCore" Version="1.0.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.2" />
    <PackageReference Include="System.Text.Json" Version="4.7.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Domain\Domain.csproj" />
  </ItemGroup>

</Project>
